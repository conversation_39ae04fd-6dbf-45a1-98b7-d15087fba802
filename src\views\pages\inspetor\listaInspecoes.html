<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspeções - InfraWatch</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/disruption.png" />
    <style>
        [x-cloak] {
            display: none !important;
        }

        .nav-item-active {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
            background-color: #eff6ff;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="listaInspecoes.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>                    <nav class="flex flex-1 justify-center">
                        <div class="flex flex-row gap-x-6 items-center">
                            <a class="text-[#111518] text-sm font-medium leading-normal" href="listaInspecoes.html">Inspeções</a>
                            <a class="text-[#111518] text-sm font-medium leading-normal" href="relatorios.html">Relatórios</a>
                        </div>
                    </nav>

                <div class="flex justify-end gap-8 items-center">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Nova inspeção atribuída.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Prazo de inspeção se aproximando.
                                </a>
                            </div>                        </div>
                    </div>                    <a href="perfil.html" class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('../ícones/perfil.png');">
                    </a>
                </div>
            </header>

            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
                    <div class="flex flex-wrap justify-between gap-3 p-4 items-center">
                        <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight min-w-72">
                            Minhas Inspeções</p>
                    </div>

                    <div class="px-4 py-3">
                        <label class="flex flex-col min-w-40 h-12 w-full">
                            <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                                <div class="text-[#637688] flex border-none bg-[#f0f2f4] items-center justify-center pl-4 rounded-l-xl border-r-0"
                                    data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                        fill="currentColor" viewBox="0 0 256 256">
                                        <path
                                            d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z">
                                        </path>
                                    </svg>
                                </div>
                                <input placeholder="Buscar inspeções..."
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-r-xl text-[#111518] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] h-full placeholder:text-[#637688] px-4 text-base font-normal leading-normal"
                                    value="" />
                            </div>
                        </label>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 px-4 py-3">
                        <div>
                            <label for="statusFilter" class="block text-sm font-medium text-gray-700">Filtrar por Status</label>
                            <select id="statusFilter" name="statusFilter"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
                                <option value="">Todos</option>
                                <option value="Agendada">Agendada</option>
                                <option value="Em Andamento">Em Andamento</option>
                                <option value="Concluída">Concluída</option>
                            </select>
                        </div>
                        <div>
                            <label for="startDateFilter" class="block text-sm font-medium text-gray-700">Data de Início</label>
                            <input type="date" id="startDateFilter" name="startDateFilter"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
                        </div>
                        <div>
                            <label for="endDateFilter" class="block text-sm font-medium text-gray-700">Data de Término</label>
                            <input type="date" id="endDateFilter" name="endDateFilter"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
                        </div>
                    </div>

                    <div class="px-4 py-3 @container">
                        <div class="flex overflow-hidden rounded-xl border border-[#dce1e5] bg-white">
                            <table class="flex-1">
                                <thead>
                                    <tr class="bg-white">
                                        <th class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">ID da Inspeção</th>
                                        <th class="px-4 py-3 text-center text-[#111518] w-[250px] text-sm font-medium leading-normal">Endereço</th>
                                        <th class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">Status</th>
                                        <th class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">Data de Início</th>
                                        <th class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">Data de Término</th>
                                        <th class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Exemplo de Linha de Inspeção (substituir com dados dinâmicos) -->
                                    <tr class="border-t border-t-[#dce1e5]">
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">INSP-001</td>
                                        <td class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">Rua das Palmeiras, 123, Cidade Exemplo</td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#fffbe6] text-[#faad14]">Em Andamento</span>
                                        </td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">25/05/2025</td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">28/05/2025</td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <a href="detalhesInspecao.html?id=INSP-001" class="text-blue-600 hover:underline mr-2">Ver Detalhes</a>
                                            <a href="iniciarInspecao.html?id=INSP-001" class="text-green-600 hover:underline">Continuar</a>
                                        </td>
                                    </tr>
                                    <tr class="border-t border-t-[#dce1e5]">
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">INSP-002</td>
                                        <td class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">Av. dos Bosques, 456, Vila Verde</td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#e6f7ff] text-[#007bff]">Agendada</span>
                                        </td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">10/06/2025</td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">12/06/2025</td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <a href="detalhesInspecao.html?id=INSP-002" class="text-blue-600 hover:underline mr-2">Ver Detalhes</a>
                                            <a href="iniciarInspecao.html?id=INSP-002" class="text-green-600 hover:underline">Iniciar</a>
                                        </td>
                                    </tr>
                                    <tr class="border-t border-t-[#dce1e5]">
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">INSP-003</td>
                                        <td class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">Praça da Matriz, 789</td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#f6ffed] text-[#52c41a]">Concluída</span>
                                        </td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">15/05/2025</td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">17/05/2025</td>
                                        <td class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <a href="detalhesInspecao.html?id=INSP-003" class="text-blue-600 hover:underline">Ver Detalhes</a>
                                        </td>
                                    </tr>
                                    <!-- Adicionar mais linhas conforme necessário -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="flex justify-center px-4 py-5">
                        <nav aria-label="Pagination">
                            <ul class="inline-flex items-center -space-x-px rounded-md text-sm">
                                <li>
                                    <a href="#"
                                        class="inline-flex items-center rounded-l-md border border-gray-300 bg-white px-3 py-2 text-gray-500 hover:bg-gray-50 focus:z-20">
                                        <span class="sr-only">Previous</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                            fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                </li>
                                <li>
                                    <a href="#" aria-current="page"
                                        class="z-10 inline-flex items-center border border-blue-500 bg-blue-50 px-3 py-2 text-blue-600 focus:z-20">1</a>
                                </li>
                                <li>
                                    <a href="#"
                                        class="inline-flex items-center border border-gray-300 bg-white px-3 py-2 text-gray-500 hover:bg-gray-50 focus:z-20">2</a>
                                </li>
                                <li>
                                    <a href="#"
                                        class="inline-flex items-center rounded-r-md border border-gray-300 bg-white px-3 py-2 text-gray-500 hover:bg-gray-50 focus:z-20">
                                        <span class="sr-only">Next</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                            fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {            // Mock de dados de inspeção - Em um aplicativo real, isso viria de um backend
            const inspections = [
                {
                    id: "INSP-001",
                    address: "Rua das Palmeiras, 123, Cidade Exemplo",
                    status: "Em Andamento",
                    startDate: "15/03/2024",
                    endDate: "30/03/2024",
                    client: "Edifício Central",
                    type: "Estrutural",
                    engineer: "Carlos Silva",
                    contactName: "Ana Paula",
                    contactEmail: "<EMAIL>",
                    contactPhone: "(11) 98765-4321"
                },
                {
                    id: "INSP-002",
                    address: "Avenida Central, 456, Vila Modelo",
                    status: "Agendada",
                    startDate: "10/06/2025",
                    endDate: "12/06/2025",
                    client: "Shopping Plaza",
                    type: "Elétrica",
                    engineer: "Fernanda Costa",
                    contactName: "Pedro Martins",
                    contactEmail: "<EMAIL>",
                    contactPhone: "(21) 91234-5678"
                },
                {
                    id: "INSP-003",
                    address: "Praça da Matriz, 789, Centro",
                    status: "Concluída",
                    startDate: "15/05/2025",
                    endDate: "17/05/2025",
                    client: "Governo Municipal",
                    type: "Viaduto",
                    engineer: "Roberto Almeida",
                    contactName: "Sofia Pereira",
                    contactEmail: "<EMAIL>",
                    contactPhone: "(31) 99999-8888"
                }
            ];

            const tableBody = document.querySelector('tbody');
            const statusFilter = document.getElementById('statusFilter');
            const startDateFilter = document.getElementById('startDateFilter');
            const endDateFilter = document.getElementById('endDateFilter');
            const searchInput = document.querySelector('input[placeholder="Buscar inspeções..."]');

            function getStatusClass(status) {
                switch (status) {
                    case "Agendada":
                        return 'bg-[#e6f7ff] text-[#007bff]';
                    case "Em Andamento":
                        return 'bg-[#fffbe6] text-[#faad14]';
                    case "Concluída":
                        return 'bg-[#f6ffed] text-[#52c41a]';
                    case "Cancelada":
                        return 'bg-gray-200 text-gray-800'; // Estilo para Cancelada
                    default:
                        return 'bg-gray-100 text-gray-600'; // Fallback
                }
            }

            function renderTable(filteredInspections) {
                tableBody.innerHTML = ''; // Limpa a tabela antes de renderizar
                if (filteredInspections.length === 0) {
                    const row = tableBody.insertRow();
                    const cell = row.insertCell();
                    cell.colSpan = 6; // Número de colunas na tabela
                    cell.className = 'h-[72px] px-4 py-2 text-center text-gray-500';
                    cell.textContent = 'Nenhuma inspeção encontrada com os filtros aplicados.';
                    return;
                }

                filteredInspections.forEach(inspection => {
                    const row = tableBody.insertRow();
                    row.className = 'border-t border-t-[#dce1e5]';

                    row.insertCell().outerHTML = `<td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">${inspection.id}</td>`;
                    row.insertCell().outerHTML = `<td class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">${inspection.address}</td>`;
                    
                    const statusCell = row.insertCell();
                    statusCell.className = 'h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center';
                    statusCell.innerHTML = `<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusClass(inspection.status)}">${inspection.status}</span>`;

                    row.insertCell().outerHTML = `<td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">${inspection.startDate}</td>`;
                    row.insertCell().outerHTML = `<td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">${inspection.endDate || 'N/A'}</td>`;
                    
                    const actionsCell = row.insertCell();
                    actionsCell.className = 'h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center';
                    let actionsHTML = `<a href="detalhesInspecao.html?id=${inspection.id}" class="text-blue-600 hover:underline mr-2">Ver Detalhes</a>`;
                    if (inspection.status === "Agendada") {
                        actionsHTML += `<a href="iniciarInspecao.html?id=${inspection.id}" class="text-green-600 hover:underline">Iniciar</a>`;
                    } else if (inspection.status === "Em Andamento") {
                        actionsHTML += `<a href="iniciarInspecao.html?id=${inspection.id}" class="text-green-600 hover:underline">Continuar</a>`;
                    }
                    actionsCell.innerHTML = actionsHTML;
                });
            }

            function filterAndRender() {
                const searchTerm = searchInput.value.toLowerCase();
                const statusValue = statusFilter.value;
                const startDateValue = startDateFilter.value;
                const endDateValue = endDateFilter.value;

                const filterStartDateTime = startDateValue ? new Date(startDateValue + "T00:00:00Z").getTime() : null;
                const filterEndDateTime = endDateValue ? new Date(endDateValue + "T23:59:59Z").getTime() : null;

                const filteredInspections = inspections.filter(inspection => {
                    const inspectionName = inspection.client ? inspection.client.toLowerCase() : '';
                    const inspectionAddress = inspection.address.toLowerCase();
                    const inspectionId = inspection.id.toLowerCase();

                    const matchesSearch = searchTerm ? 
                                        inspectionName.includes(searchTerm) || 
                                        inspectionAddress.includes(searchTerm) || 
                                        inspectionId.includes(searchTerm) : true;

                    const matchesStatus = statusValue ? inspection.status === statusValue : true;

                    let matchesDate = true;
                    if (filterStartDateTime || filterEndDateTime) {
                        const startParts = inspection.startDate.split('/');
                        const inspectionStartDateTime = startParts.length === 3 ? new Date(Date.UTC(parseInt(startParts[2]), parseInt(startParts[1]) - 1, parseInt(startParts[0]))).getTime() : null;
                        
                        const endParts = inspection.endDate ? inspection.endDate.split('/') : null;
                        const inspectionEndDateTime = endParts && endParts.length === 3 ? new Date(Date.UTC(parseInt(endParts[2]), parseInt(endParts[1]) - 1, parseInt(endParts[0]))).getTime() : null;

                        if (filterStartDateTime && inspectionStartDateTime) {
                            matchesDate = matchesDate && (inspectionStartDateTime >= filterStartDateTime);
                        }
                        if (filterEndDateTime && inspectionEndDateTime) {
                             matchesDate = matchesDate && (inspectionEndDateTime <= filterEndDateTime);
                        } else if (filterEndDateTime && inspection.status !== "Concluída" && inspectionStartDateTime) {
                            // Se a data de término do filtro está definida, mas a inspeção não está concluída (não tem data de término real),
                            // e a data de início da inspeção é posterior à data de término do filtro, não deve corresponder.
                            matchesDate = matchesDate && (inspectionStartDateTime <= filterEndDateTime); 
                        } else if (filterEndDateTime && !inspectionEndDateTime && inspection.status === "Concluída"){
                            // Caso uma inspeção concluída não tenha data de término, não deve corresponder se o filtro de data final estiver ativo.
                            matchesDate = false;
                        }
                    }
                    return matchesSearch && matchesStatus && matchesDate;
                });
                renderTable(filteredInspections);
            }

            // Adiciona a opção "Cancelada" ao filtro de status, se ainda não existir
            if (!Array.from(statusFilter.options).find(opt => opt.value === "Cancelada")){
                const canceladaOption = document.createElement('option');
                canceladaOption.value = "Cancelada";
                canceladaOption.textContent = "Cancelada";
                statusFilter.appendChild(canceladaOption);
            }

            // Event listeners para os filtros e busca
            statusFilter.addEventListener('change', filterAndRender);
            startDateFilter.addEventListener('input', filterAndRender);
            endDateFilter.addEventListener('input', filterAndRender);
            searchInput.addEventListener('input', filterAndRender);

            // Renderiza a tabela inicialmente com todos os dados
            renderTable(inspections);
        });
    </script>
</body>

</html>
