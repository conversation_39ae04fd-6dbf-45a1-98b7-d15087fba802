<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes do Sistema - Inspetor</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/disruption.png" />
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="listaInspecoes.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>
                  <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="listaInspecoes.html">Inspeções</a>
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="relatorios.html">Relatórios</a>
                    </div>
                </nav>

                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Inspeção INSP-001 atribuída.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Prazo da inspeção INSP-002 se aproximando.
                                </a>
                            </div>
                        </div>
                    </div>                    <a href="perfil.html" class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('../ícones/perfil.png');">
                    </a>
                </div>
            </header>

            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1" x-data="systemDetails()">
                    <!-- Cabeçalho do Sistema -->
                    <div class="flex flex-wrap justify-between gap-3 p-4">
                        <div class="flex min-w-72 flex-col gap-3">
                            <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight">
                                <span x-text="system.name"></span>
                            </p>
                            <p class="text-[#637588] text-sm font-normal leading-normal">
                                ID: <span x-text="system.id"></span> | Status: 
                                <span class="inline-block px-2 py-1 rounded text-xs font-medium"
                                      :class="getStatusColor(system.status)" 
                                      x-text="system.status"></span>
                            </p>
                        </div>
                        <div class="flex gap-3">
                            <button @click="toggleEditMode()" 
                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#1980e6] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                                <span x-text="editMode ? 'Salvar' : 'Editar'"></span>
                            </button>
                        </div>
                    </div>

                    <!-- Navegação Breadcrumb -->
                    <div class="px-4 py-2 text-sm text-gray-600">
                        <a href="detalhesInspecao.html" class="text-blue-600 hover:text-blue-800" x-text="`Inspeção ${inspectionId}`"></a>
                        <span class="mx-2">></span>
                        <a href="detalhesEdificio.html" class="text-blue-600 hover:text-blue-800" x-text="`Edifício ${buildingId}`"></a>
                        <span class="mx-2">></span>
                        <a href="detalhesPavimento.html" class="text-blue-600 hover:text-blue-800" x-text="`Pavimento ${floorId}`"></a>
                        <span class="mx-2">></span>
                        <a href="detalhesAmbiente.html" class="text-blue-600 hover:text-blue-800" x-text="`Ambiente ${environmentId}`"></a>
                        <span class="mx-2">></span>
                        <span x-text="system.name"></span>
                    </div>

                    <!-- Informações Básicas -->
                    <div class="grid grid-cols-2 gap-4 px-4 py-4">
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Nome do Sistema</p>
                            <input x-model="system.name" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                   type="text">
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Tipo</p>
                            <select x-model="system.type" 
                                    :disabled="!editMode"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed">
                                <option value="Elétrico">Elétrico</option>
                                <option value="Hidráulico">Hidráulico</option>
                                <option value="HVAC">HVAC</option>
                                <option value="Segurança">Segurança</option>
                                <option value="Incêndio">Incêndio</option>
                                <option value="Estrutural">Estrutural</option>
                                <option value="Comunicação">Comunicação</option>
                            </select>
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Fabricante</p>
                            <input x-model="system.manufacturer" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                   type="text">
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Modelo</p>
                            <input x-model="system.model" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                   type="text">
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Data de Instalação</p>
                            <input x-model="system.installation_date" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                   type="date">
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Última Manutenção</p>
                            <input x-model="system.last_maintenance" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                   type="date">
                        </div>
                    </div>

                    <!-- Descrição -->
                    <div class="px-4 py-4">
                        <p class="text-[#111518] text-base font-medium leading-normal mb-2">Descrição</p>
                        <textarea x-model="system.description" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                  rows="3"
                                  placeholder="Descrição detalhada do sistema..."></textarea>
                    </div>

                    <!-- Especificações Técnicas -->
                    <div class="px-4 py-4">
                        <p class="text-[#111518] text-base font-medium leading-normal mb-2">Especificações Técnicas</p>
                        <textarea x-model="system.specifications" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                  rows="4"
                                  placeholder="Especificações técnicas do sistema (voltagem, potência, capacidade, etc.)..."></textarea>
                    </div>

                    <!-- Observações -->
                    <div class="px-4 py-4">
                        <p class="text-[#111518] text-base font-medium leading-normal mb-2">Observações</p>
                        <textarea x-model="system.observations" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                                  rows="3"
                                  placeholder="Observações específicas sobre o sistema..."></textarea>
                    </div>

                    <!-- Fotos do Sistema -->
                    <div class="px-4 py-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-[#111518] text-lg font-bold leading-tight">Fotos do Sistema</h3>
                            <button x-show="editMode" @click="addPhoto()" 
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                Adicionar Foto
                            </button>
                        </div>
                        <div class="grid grid-cols-3 gap-4" x-show="system.photos && system.photos.length > 0">
                            <template x-for="(photo, index) in system.photos" :key="index">
                                <div class="relative group">
                                    <img :src="photo.url" :alt="photo.description" 
                                         class="w-full h-32 object-cover rounded-lg cursor-pointer"
                                         @click="viewPhoto(photo)">
                                    <div x-show="editMode" class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <button @click="removePhoto(index)" 
                                                class="bg-red-500 text-white rounded-full p-1 hover:bg-red-600">
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="mt-2 text-sm text-gray-600" x-text="photo.description"></p>
                                </div>
                            </template>
                        </div>
                        <div x-show="!system.photos || system.photos.length === 0" 
                             class="text-center py-8 text-gray-500">
                            Nenhuma foto adicionada ainda
                        </div>
                    </div>

                    <!-- Lista de Patologias -->
                    <div class="px-4 py-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-[#111518] text-lg font-bold leading-tight">Patologias Identificadas</h3>
                            <button x-show="editMode" @click="addPathology()" 
                                    class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                                Adicionar Patologia
                            </button>
                        </div>
                        <div class="space-y-3">
                            <template x-for="pathology in system.pathologies" :key="pathology.id">
                                <div class="border border-red-200 rounded-lg p-4 hover:shadow-md transition-shadow bg-red-50">
                                    <div class="flex justify-between items-center">
                                        <div class="flex-1">
                                            <h4 class="font-medium text-gray-900" x-text="pathology.name"></h4>
                                            <p class="text-sm text-gray-600" x-text="pathology.description"></p>
                                            <div class="mt-2 flex gap-4 text-xs text-gray-500">
                                                <span>Severidade: 
                                                    <span class="inline-block px-2 py-1 rounded text-xs font-medium"
                                                          :class="getSeverityColor(pathology.severity)" 
                                                          x-text="pathology.severity"></span>
                                                </span>
                                                <span>Status: 
                                                    <span class="inline-block px-2 py-1 rounded text-xs font-medium"
                                                          :class="getStatusColor(pathology.status)" 
                                                          x-text="pathology.status"></span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex gap-2">
                                            <button @click="viewPathologyDetails(pathology.id)" 
                                                    class="px-3 py-1 text-sm bg-red-100 text-red-800 rounded hover:bg-red-200">
                                                Ver Detalhes
                                            </button>
                                            <button x-show="editMode" @click="removePathology(pathology.id)" 
                                                    class="px-3 py-1 text-sm bg-red-200 text-red-900 rounded hover:bg-red-300">
                                                Remover
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div x-show="!system.pathologies || system.pathologies.length === 0" 
                             class="text-center py-8 text-gray-500">
                            Nenhuma patologia identificada
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para visualizar/editar fotos -->
    <div x-show="showPhotoModal" @click.away="closePhotoModal()" 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-cloak>
        <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">Foto</h3>
                <button @click="closePhotoModal()" class="text-gray-500 hover:text-gray-700">
                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div x-show="currentPhoto">
                <img :src="currentPhoto?.url" :alt="currentPhoto?.description" 
                     class="w-full max-h-96 object-contain mb-4">
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium mb-1">Descrição</label>
                        <textarea x-model="currentPhoto.description" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                                  rows="3"></textarea>
                    </div>
                    <div x-show="editMode" class="flex gap-3">
                        <button @click="savePhotoDescription()" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Salvar
                        </button>
                        <button @click="closePhotoModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Cancelar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function systemDetails() {
            return {
                editMode: false,
                showPhotoModal: false,
                currentPhoto: null,
                inspectionId: '',
                buildingId: '',
                floorId: '',
                environmentId: '',
                system: {
                    id: '',
                    name: '',
                    type: '',
                    manufacturer: '',
                    model: '',
                    installation_date: '',
                    last_maintenance: '',
                    status: '',
                    description: '',
                    specifications: '',
                    observations: '',
                    photos: [],
                    pathologies: []
                },

                init() {
                    this.loadSystem();
                },                loadSystem() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const systemId = urlParams.get('id');
                    this.environmentId = urlParams.get('environment');
                    this.floorId = urlParams.get('floor');
                    this.buildingId = urlParams.get('building');
                    this.inspectionId = urlParams.get('inspection');
                    
                    // Dados sincronizados do sistema
                    const systemsData = {
                        'SIS-001': {
                            id: 'SIS-001',
                            name: 'Sistema Elétrico Principal',
                            type: 'Elétrico',
                            manufacturer: 'Schneider Electric',
                            model: 'Quadro QDC-100A',
                            installation_date: '2020-01-15',
                            last_maintenance: '2024-01-15',
                            status: 'Ativo',
                            description: 'Sistema elétrico principal do lobby, incluindo quadro de distribuição geral, iluminação LED, tomadas e sistema de emergência com autonomia de 2 horas.',
                            specifications: 'Tensão: 220V/380V\nPotência: 25kW\nCapacidade: 100A\nEficiência: 98.5%\nProteção: DR 30mA',
                            observations: 'Sistema em bom estado geral, necessita manutenção preventiva periódica.',
                            photos: [
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Quadro de distribuição principal'
                                },
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Sistema de iluminação LED'
                                },
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Sistema de emergência'
                                }
                            ],
                            pathologies: [
                                {
                                    id: 'PAT-001',
                                    name: 'Oxidação nos Conectores',
                                    description: 'Presença de oxidação leve em conectores do quadro principal',
                                    severity: 'Baixa',
                                    status: 'Identificada'
                                },
                                {
                                    id: 'PAT-002',
                                    name: 'Desgaste nos Disjuntores',
                                    description: 'Disjuntores apresentando sinais de desgaste normal',
                                    severity: 'Baixa',
                                    status: 'Em Análise'
                                }
                            ]
                        },
                        'SIS-002': {
                            id: 'SIS-002',
                            name: 'Sistema Hidráulico',
                            type: 'Hidráulico',
                            manufacturer: 'Tigre',
                            model: 'Sistema Predial Completo',
                            installation_date: '2019-06-20',
                            last_maintenance: '2024-02-10',
                            status: 'Em Manutenção',
                            description: 'Sistema hidráulico da área de alimentação, incluindo tubulações de água fria, quente, sistema de pressurização e pontos de consumo.',
                            specifications: 'Capacidade: 2000L/h\nPotência: 5kW (bombas)\nEficiência: 92%\nPressão: 40mca',
                            observations: 'Sistema em manutenção preventiva, aguardando reparos em tubulação.',
                            photos: [
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Casa de bombas'
                                },
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Tubulações principais'
                                }
                            ],
                            pathologies: [
                                {
                                    id: 'PAT-003',
                                    name: 'Vazamento na Tubulação',
                                    description: 'Pequeno vazamento identificado na tubulação de água fria',
                                    severity: 'Média',
                                    status: 'Em Reparo'
                                }
                            ]
                        },
                        'SIS-003': {
                            id: 'SIS-003',
                            name: 'Sistema de Climatização',
                            type: 'HVAC',
                            manufacturer: 'Carrier',
                            model: 'Chiller 30XA',
                            installation_date: '2021-03-10',
                            last_maintenance: '2024-03-01',
                            status: 'Ativo',
                            description: 'Sistema de climatização central do salão de festas, incluindo unidades condensadoras, evaporadoras e sistema de dutos.',
                            specifications: 'Capacidade: 60TR\nVoltagem: 380V\nPotência: 45kW\nEficiência: 95%',
                            observations: 'Sistema funcionando perfeitamente, próxima manutenção agendada.',
                            photos: [
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Unidade condensadora'
                                },
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Sistema de dutos'
                                }
                            ],
                            pathologies: []
                        }
                    };

                    if (systemId && systemsData[systemId]) {
                        this.system = { ...systemsData[systemId] };
                    } else {
                        this.system = { ...systemsData['SIS-001'] };
                    }
                },

                toggleEditMode() {
                    if (this.editMode) {
                        this.saveSystem();
                    }
                    this.editMode = !this.editMode;
                },

                saveSystem() {
                    console.log('Salvando sistema:', this.system);
                    alert('Sistema salvo com sucesso!');
                },                getStatusColor(status) {
                    const colors = {
                        'Ativo': 'bg-[#f6ffed] text-[#52c41a]',
                        'Em Andamento': 'bg-[#fffbe6] text-[#faad14]',
                        'Em Manutenção': 'bg-[#fff2e8] text-[#fa8c16]',
                        'Inativo': 'bg-[#f5f5f5] text-[#8c8c8c]',
                        'Concluído': 'bg-[#f6ffed] text-[#52c41a]',
                        'Pendente': 'bg-[#e6f7ff] text-[#007bff]',
                        'Identificada': 'bg-[#fff2e8] text-[#fa8c16]',
                        'Em Análise': 'bg-[#f9f0ff] text-[#722ed1]',
                        'Em Reparo': 'bg-[#fffbe6] text-[#faad14]'
                    };
                    return colors[status] || 'bg-[#f5f5f5] text-[#8c8c8c]';
                },

                getSeverityColor(severity) {
                    const colors = {
                        'Baixa': 'bg-green-100 text-green-800',
                        'Média': 'bg-yellow-100 text-yellow-800',
                        'Alta': 'bg-red-100 text-red-800',
                        'Crítica': 'bg-red-200 text-red-900'
                    };
                    return colors[severity] || 'bg-gray-100 text-gray-800';
                },

                addPhoto() {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'image/*';
                    input.onchange = (e) => {
                        const file = e.target.files[0];
                        if (file) {
                            const url = URL.createObjectURL(file);
                            this.system.photos.push({
                                url: url,
                                description: `Nova foto - ${new Date().toLocaleString()}`
                            });
                        }
                    };
                    input.click();
                },

                removePhoto(index) {
                    if (confirm('Tem certeza que deseja remover esta foto?')) {
                        this.system.photos.splice(index, 1);
                    }
                },

                viewPhoto(photo) {
                    this.currentPhoto = { ...photo };
                    this.showPhotoModal = true;
                },

                closePhotoModal() {
                    this.showPhotoModal = false;
                    this.currentPhoto = null;
                },

                savePhotoDescription() {
                    const index = this.system.photos.findIndex(p => p.url === this.currentPhoto.url);
                    if (index !== -1) {
                        this.system.photos[index].description = this.currentPhoto.description;
                    }
                    this.closePhotoModal();
                },

                addPathology() {
                    const newPathology = {
                        id: `PATH-${Date.now()}`,
                        name: 'Nova Patologia',
                        description: 'Descrição da patologia',
                        severity: 'Média',
                        status: 'Identificada'
                    };
                    this.system.pathologies.push(newPathology);
                },

                removePathology(pathologyId) {
                    if (confirm('Tem certeza que deseja remover esta patologia?')) {
                        this.system.pathologies = this.system.pathologies.filter(p => p.id !== pathologyId);
                    }
                },

                viewPathologyDetails(pathologyId) {
                    window.location.href = `detalhesPatologia.html?id=${pathologyId}&system=${this.system.id}&environment=${this.environmentId}&floor=${this.floorId}&building=${this.buildingId}&inspection=${this.inspectionId}`;
                }
            }
        }

        function goBack() {
            const urlParams = new URLSearchParams(window.location.search);
            const environmentId = urlParams.get('environment');
            const floorId = urlParams.get('floor');
            const buildingId = urlParams.get('building');
            const inspectionId = urlParams.get('inspection');
            
            if (environmentId && floorId && buildingId && inspectionId) {
                window.location.href = `detalhesAmbiente.html?id=${environmentId}&floor=${floorId}&building=${buildingId}&inspection=${inspectionId}`;
            } else {
                window.location.href = 'listaInspecoes.html';
            }
        }
    </script>
</body>

</html>