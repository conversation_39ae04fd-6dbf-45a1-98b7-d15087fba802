require('dotenv').config();
const express = require('express');
const app = express();
const db = require('./config/db');
const path = require('path');

app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

db.connect()
  .then(() => {
    console.log('Conectado ao banco de dados PostgreSQL');

    app.use(express.json());

    const usuarioRoutes = require('./routes/usuarioRoutes');
    app.use('/usuarios', usuarioRoutes);

    const relatoriosRoutes = require('./routes/relatoriosRoutes');
    app.use('/relatorios', relatoriosRoutes);

    const inspecoesRoutes = require('./routes/inspecoesRoutes');
    app.use('/inspecoes', inspecoesRoutes);

    const funcoes_usuarioRoutes = require('./routes/funcoes_usuarioRoutes');
    app.use('/funcoes_usuario', funcoes_usuarioRoutes);

    const equipes_inspecaoRoutes = require('./routes/equipes_inspecaoRoutes');
    app.use('/equipes_inspecao', equipes_inspecaoRoutes);
    
    const edificiosRoutes = require('./routes/edificiosRoutes');
    app.use('/edificio', edificiosRoutes);

    const pavimentosRoutes = require('./routes/pavimentosRoutes');
    app.use('/pavimento', pavimentosRoutes);

    const ambientesRoutes = require('./routes/ambientesRoutes');
    app.use('/ambiente', ambientesRoutes);
  
    const sistemasRoutes = require('./routes/sistemasRoutes');
    app.use('/sistemas', sistemasRoutes);

    const patologiasRoutes = require('./routes/patologiasRoutes');
    app.use('/patologias', patologiasRoutes);

    const fotosRoutes = require('./routes/fotosRoutes');
    app.use('/fotos', fotosRoutes);

    const frontendRoutes = require('./routes/frontRoutes');
    app.use('/', frontendRoutes);

    // Middleware para lidar com erros de rota não encontrada
    app.use((req, res, next) => {
      res.status(404).send('Página não encontrada');
    });

    // Middleware para lidar com erros internos do servidor
    app.use((err, req, res, next) => {
      console.error(err.stack);
      res.status(500).send('Erro no servidor');
    });

    const PORT = process.env.PORT || 3000;
    app.listen(PORT, () => {
      console.log(`Servidor rodando na porta ${PORT}`);
    });
  })
  .catch(err => {
    console.error('Erro ao conectar ao banco de dados:', err);
  });
