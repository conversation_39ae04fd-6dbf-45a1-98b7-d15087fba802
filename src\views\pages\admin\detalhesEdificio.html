<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <title>Detalhes do Edifício</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="dashboard.html">
                        <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>
                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="dashboard.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/botao-de-inicio.png" alt="Painel" width="20px" height="20px">
                            Painel
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                        <a href="membros.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/grupo.png" alt="Membros" width="20px" height="20px">
                            Membros
                        </a>
                    </div>
                </nav>
                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Inspeção #12350 atribuída à Equipe X.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Relatório da inspeção #12345 pronto.
                                </a>
                                <div x-show="!hasNotifications" class="px-4 py-3 text-sm text-gray-500">
                                    Nenhuma nova notificação.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('../ícones/perfil.png');">
                    </div>
                </div>
            </header>

            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1" x-data="buildingDetails">
                    <div class="flex flex-wrap justify-between gap-3 p-4">
                        <div class="flex min-w-72 flex-col gap-3">
                            <div class="flex items-center gap-3">
                                <button id="backButton" onclick="goBack()"
                                    class="flex items-center justify-center rounded-lg h-10 bg-[#f0f2f4] px-3 text-[#111518] hover:bg-gray-300 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="m12 19-7-7 7-7" />
                                        <path d="M19 12H5" />
                                    </svg>
                                    <span class="ml-2">Voltar</span>
                                </button>
                                <p class="text-[#111518] text-[32px] font-bold leading-tight tracking-[-0.015em]"
                                    x-text="'Edifício ' + building.id"></p>
                            </div>
                        </div>
                    </div>

                    <!-- Informações Básicas -->
                    <div class="grid grid-cols-2 gap-4 px-4 py-4">
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Informações Básicas</h3>
                            <div class="space-y-2">
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Nome:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="building.name"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Endereço:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="building.address"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Tipo:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="building.type"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Ano de Construção:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="building.constructionYear"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Características</h3>
                            <div class="space-y-2">
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Área Total:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="building.totalArea + ' m²'"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Número de Pavimentos:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="building.floors.length"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Status:</span>
                                    <span
                                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getStatusColor(building.status)" x-text="building.status"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Última Inspeção:</span>
                                    <span class="ml-2 text-sm text-gray-900"
                                        x-text="building.lastInspection || 'Nunca'"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg border border-gray-200 col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Descrição</h3>
                            <p class="text-sm text-gray-700" x-text="building.description"></p>
                        </div>
                    </div>

                    <!-- Fotos do Edifício -->
                    <div class="px-4 py-4" x-show="building.photos && building.photos.length > 0">
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Fotos do Edifício</h3>
                            <div class="grid grid-cols-3 gap-4">
                                <template x-for="photo in building.photos" :key="photo.id">
                                    <div class="relative">
                                        <img :src="photo.url" :alt="photo.description"
                                            class="w-full h-32 object-cover rounded-lg">
                                        <div class="mt-2">
                                            <p class="text-xs text-gray-600" x-text="photo.description"></p>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- Lista de Pavimentos -->
                    <div class="flex flex-col gap-3 p-4">
                        <h3 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">Pavimentos</h3>
                        <template x-for="floor in building.floors" :key="floor.id">
                            <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 rounded-lg border border-gray-200 cursor-pointer hover:bg-gray-50"
                                @click="viewFloorDetails(floor.id)">
                                <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-lg w-14 h-14"
                                    :style="`background-image: url('${floor.photo || '../ícones/disruption.png'}')`">
                                </div>
                                <div class="flex flex-col justify-center flex-1">
                                    <p class="text-[#111518] text-base font-medium leading-normal" x-text="floor.name">
                                    </p>
                                    <p class="text-[#637588] text-sm font-normal leading-normal"
                                        x-text="floor.description"></p>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="text-sm text-gray-500" x-text="floor.environments?.length || 0"></span>
                                    <span class="text-sm text-gray-500">ambientes</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="m9 18 6-6-6-6" />
                                    </svg>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function buildingDetails() {
            return {
                building: {},

                init() {
                    this.loadBuilding();
                },                loadBuilding() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const buildingId = urlParams.get('id');

                    // Dados sincronizados do edifício
                    const buildingsData = {
                        'ED-001': {
                            id: 'ED-001',
                            name: 'Edifício Central',
                            address: 'Av. Paulista, 1500 - Bela Vista, São Paulo - SP',
                            type: 'Comercial',
                            constructionYear: 2018,
                            totalArea: 12500,
                            status: 'Ativo',
                            lastInspection: '15/05/2025',
                            description: 'Edifício comercial moderno de 15 andares com fachada em vidro e estrutura em concreto armado. Possui certificação LEED Gold e sistemas inteligentes de automação predial.',
                            photos: [
                                {
                                    id: 1,
                                    url: '../ícones/apartamento.png',
                                    description: 'Fachada principal - Vista da Avenida Paulista'
                                },
                                {
                                    id: 2,
                                    url: '../ícones/apartamento.png',
                                    description: 'Hall de entrada principal'
                                },
                                {
                                    id: 3,
                                    url: '../ícones/apartamento.png',
                                    description: 'Vista lateral do edifício'
                                }
                            ],
                            floors: [
                                {
                                    id: 'PAV-001',
                                    name: 'Térreo',
                                    description: 'Hall de entrada, recepção e área comercial',
                                    photo: '../ícones/layer.png',
                                    environments: ['Hall de Entrada', 'Recepção', 'Loja 1', 'Loja 2']
                                },
                                {
                                    id: 'PAV-002',
                                    name: 'Mezanino',
                                    description: 'Área administrativa e salas de reunião',
                                    photo: '../ícones/layer.png',
                                    environments: ['Sala Administrativa', 'Sala de Reunião A', 'Sala de Reunião B']
                                },
                                {
                                    id: 'PAV-003',
                                    name: '1º Andar',
                                    description: 'Escritórios corporativos',
                                    photo: '../ícones/layer.png',
                                    environments: ['Escritório Central', 'Sala de Diretoria', 'Copa']
                                }
                            ]
                        },
                        'ED-002': {
                            id: 'ED-002',
                            name: 'Shopping Plaza',
                            address: 'Rua Augusta, 2490 - Jardins, São Paulo - SP',
                            type: 'Comercial',
                            constructionYear: 2020,
                            totalArea: 25000,
                            status: 'Ativo',
                            lastInspection: '20/05/2025',
                            description: 'Centro comercial de médio porte com 3 pisos, praça de alimentação e cinema. Estrutura moderna com sistema de climatização central.',
                            photos: [
                                {
                                    id: 1,
                                    url: '../ícones/apartamento.png',
                                    description: 'Fachada principal do shopping'
                                },
                                {
                                    id: 2,
                                    url: '../ícones/apartamento.png',
                                    description: 'Átrio central'
                                }
                            ],
                            floors: [
                                {
                                    id: 'PAV-004',
                                    name: 'Térreo',
                                    description: 'Lojas principais e praça central',
                                    photo: '../ícones/layer.png',
                                    environments: ['Praça Central', 'Loja Âncora', 'Corredor Principal']
                                },
                                {
                                    id: 'PAV-005',
                                    name: '1º Piso',
                                    description: 'Lojas diversas e serviços',
                                    photo: '../ícones/layer.png',
                                    environments: ['Corredor Norte', 'Corredor Sul', 'Área de Serviços']
                                }
                            ]
                        },
                        'ED-003': {
                            id: 'ED-003',
                            name: 'Residencial Aurora',
                            address: 'Rua das Flores, 800 - Moema, São Paulo - SP',
                            type: 'Residencial',
                            constructionYear: 2015,
                            totalArea: 8500,
                            status: 'Ativo',
                            lastInspection: '10/06/2025',
                            description: 'Condomínio residencial com 8 andares, apartamentos de 2 e 3 quartos. Possui área de lazer completa e sistema de segurança 24h.',
                            photos: [
                                {
                                    id: 1,
                                    url: '../ícones/apartamento.png',
                                    description: 'Fachada do condomínio'
                                },
                                {
                                    id: 2,
                                    url: '../ícones/apartamento.png',
                                    description: 'Área de lazer'
                                }
                            ],
                            floors: [
                                {
                                    id: 'PAV-006',
                                    name: 'Térreo',
                                    description: 'Portaria, salão de festas e garagem',
                                    photo: '../ícones/layer.png',
                                    environments: ['Portaria', 'Salão de Festas', 'Garagem']
                                },
                                {
                                    id: 'PAV-007',
                                    name: '1º Andar',
                                    description: 'Apartamentos 101 a 104',
                                    photo: '../ícones/layer.png',
                                    environments: ['Apartamento 101', 'Apartamento 102', 'Apartamento 103', 'Apartamento 104']
                                }
                            ]
                        }
                    };

                    this.building = buildingsData[buildingId] || buildingsData['ED-001'];
                },
                getStatusColor(status) {
                    switch (status) {
                        case 'Ativo':
                            return 'bg-green-100 text-green-800';
                        case 'Em Manutenção':
                            return 'bg-yellow-100 text-yellow-800';
                        case 'Inativo':
                            return 'bg-red-100 text-red-800';
                        default:
                            return 'bg-gray-100 text-gray-800';
                    }
                },

                viewFloorDetails(floorId) {
                    window.location.href = `detalhesPavimento.html?id=${floorId}`;
                }
            }
        }

        function goBack() {
            const urlParams = new URLSearchParams(window.location.search);
            const mode = urlParams.get('mode');
            const from = urlParams.get('from');
            const inspectionId = urlParams.get('inspectionId');

            if (mode === 'view' && from === 'inspection') {
                window.location.href = `detalhesInspecao.html?id=${inspectionId}`;
            } else {
                window.location.href = 'dashboard.html';
            }
        }
    </script>
</body>

</html>