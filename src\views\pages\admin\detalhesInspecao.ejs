<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <title>Detalhes da Inspeção</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="/dashboard">
                        <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>
                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="/dashboard"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="/images/icones/botao-de-inicio.png" alt="Painel" width="20px" height="20px">
                            Painel
                        </a>
                        <a href="/relatorios"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="/images/icones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                        <a href="/membros"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="/images/icones/grupo.png" alt="Membros" width="20px" height="20px">
                            Membros
                        </a>
                    </div>
                </nav>
                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Inspeção #12350 atribuída à Equipe X.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Relatório da inspeção #12345 pronto.
                                </a>
                                <div x-show="!hasNotifications" class="px-4 py-3 text-sm text-gray-500">
                                    Nenhuma nova notificação.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('../ícones/perfil.png');">
                    </div>
                </div>
            </header>            <main class="flex-1">
                <div class="px-4 sm:px-6 lg:px-8 xl:px-40 flex flex-1 justify-center py-5">
                    <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
                        <div class="flex flex-wrap justify-between items-center gap-3 p-4">                            <div class="flex items-center gap-3">
                                <button onclick="window.location.href = '/dashboard'"
                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-gray-200 text-gray-700 text-sm font-medium leading-normal hover:bg-gray-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M15.75 19.5L8.25 12l7.5-7.5" />
                                    </svg>
                                    Voltar
                                </button><h2 class="text-[#111518] tracking-light text-[28px] font-bold leading-tight">
                                    Detalhes da Inspeção <span id="inspection-title-id" class="text-gray-500 text-[22px]">#INSP-001</span>
                                </h2>
                            </div>                            <div class="flex gap-x-3">
                                <a href="editarInspecao.html?id=INSP-001"
                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#1d4ed8] text-white text-sm font-medium leading-normal hover:bg-[#1e40af]">
                                    <span class="truncate">Editar</span>
                                </a>
                            </div>
                        </div>

                        <div class="p-4 grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-white p-6 rounded-lg shadow-md border border-gray-200 flex flex-col gap-4">
                                <h3 class="text-xl font-semibold text-gray-800 mb-2">Dados da Inspeção</h3>
                                <div class="flex flex-col gap-1.5">
                                    <p class="text-gray-700 text-sm font-medium leading-normal">ID da Inspeção:</p>
                                    <p class="text-gray-600 text-sm leading-normal">INSP-ADM-001</p>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <p class="text-gray-700 text-sm font-medium leading-normal">Nome:</p>
                                    <p class="text-gray-600 text-sm leading-normal">Inspeção Edifício Principal</p>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <p class="text-gray-700 text-sm font-medium leading-normal">Endereço:</p>
                                    <p class="text-gray-600 text-sm leading-normal">Rua das Palmeiras, 123, Centro</p>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <p class="text-gray-700 text-sm font-medium leading-normal">Cliente:</p>
                                    <p class="text-gray-600 text-sm leading-normal">Condomínio Edifício Central</p>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <p class="text-gray-700 text-sm font-medium leading-normal">Tipo:</p>
                                    <p class="text-gray-600 text-sm leading-normal">Inspeção de Segurança</p>
                                </div>
                            </div>                            <div class="bg-white p-6 rounded-lg shadow-md border border-gray-200 flex flex-col gap-4">
                                <div class="flex flex-col gap-1.5">
                                    <p class="text-gray-700 text-sm font-medium leading-normal">Status:</p>
                                    <p class="text-gray-600 text-sm leading-normal"><span id="status-badge" class="px-2 py-1 text-xs font-semibold rounded-full">-</span></p>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <p class="text-gray-700 text-sm font-medium leading-normal">Responsável:</p>
                                    <p class="text-gray-600 text-sm leading-normal">João Silva</p>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <p class="text-gray-700 text-sm font-medium leading-normal">Data de Início:</p>
                                    <p class="text-gray-600 text-sm leading-normal">10/03/2024</p>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <p class="text-gray-700 text-sm font-medium leading-normal">Data de Fim:</p>
                                    <p class="text-gray-600 text-sm leading-normal">12/03/2024</p>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <p class="text-gray-700 text-sm font-medium leading-normal">Notas Adicionais:</p>
                                    <p class="text-gray-600 text-sm leading-normal whitespace-pre-wrap">Inspeção realizada conforme agendado. Todos os itens verificados.</p>
                                </div>
                            </div>
                        </div>                        <!-- Seção de Edifícios, Pavimentos, Ambientes, Sistemas e Patologias -->                        <div class="flex flex-col gap-4 p-4"
                            x-data="{ openEdificio: null, openPavimento: null, openAmbiente: null, openSistema: null }">
                            <div>                                <h3 class="text-[#111518] text-xl font-bold leading-tight tracking-[-0.015em] mb-3">
                                    Detalhamento da Inspeção
                                </h3><!-- Exemplo de Edifício -->                                <div class="mb-4 rounded-lg border border-[#d9dbde] overflow-hidden shadow-sm">
                                    <div class="flex items-center justify-between p-4 bg-white">
                                        <button @click="openEdificio = openEdificio === 'edificio1' ? null : 'edificio1'"class="flex-1 flex justify-between items-center focus:outline-none hover:bg-white/50 p-3 rounded-lg transition-all duration-200">
                                            <span class="text-lg font-bold text-[#111518]">Edifício Principal</span>
                                            <svg class="w-6 h-6 transform transition-transform duration-300"
                                                :class="{'rotate-180': openEdificio === 'edificio1'}" fill="none"
                                                stroke="currentColor" viewBox="0 0 24 24"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M19 9l-7 7-7-7"></path>
                                            </svg>
                                        </button>                                        <a href="detalhesEdificio.html?mode=view&from=inspection&inspectionId=INSP-001&edificio=edificio1"
                                            class="ml-4 px-4 py-2 bg-blue-600 text-white text-sm font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
                                            Ver Detalhes
                                        </a>
                                    </div>                                    <div x-show="openEdificio === 'edificio1'" x-collapse
                                        class="p-4 border-t border-[#dee0e3]">
                                        <p class="text-base text-gray-600 mb-4">Descrição detalhada do Edifício Principal, incluindo informações sobre sua estrutura, idade e uso.</p><!-- Exemplo de Pavimento -->
                                        <div class="mb-4 rounded-lg border border-[#e0e2e5] overflow-hidden shadow-sm">
                                            <div class="flex items-center justify-between p-4 bg-white">
                                                <button @click="openPavimento = openPavimento === 'pav1' ? null : 'pav1'"                                                    class="flex-1 flex justify-between items-center focus:outline-none hover:bg-white/50 p-3 rounded-lg transition-all duration-200">
                                                    <span class="text-base font-bold text-[#333]">Pavimento Térreo</span>
                                                    <svg class="w-5 h-5 transform transition-transform duration-300"
                                                        :class="{'rotate-180': openPavimento === 'pav1'}" fill="none"
                                                        stroke="currentColor" viewBox="0 0 24 24"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                    </svg>
                                                </button>                                                <a href="detalhesPavimento.html?mode=view&from=inspection&inspectionId=INSP-001&edificio=edificio1&pavimento=pav1"
                                                    class="ml-3 px-3 py-2 bg-blue-600 text-white text-xs font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
                                                    Ver Detalhes
                                                </a>
                                            </div>                                            <div x-show="openPavimento === 'pav1'" x-collapse
                                                class="p-4 border-t border-[#e0e2e5]">
                                                <p class="text-sm text-gray-600 mb-3">Descrição do Pavimento Térreo, como áreas comuns, acessos e características específicas.</p><!-- Exemplo de Ambiente -->                                                <div class="mb-3 rounded-lg border border-[#e5e7eb] overflow-hidden shadow-sm">
                                                    <div class="flex items-center justify-between p-3 bg-white">
                                                        <button
                                                            @click="openAmbiente = openAmbiente === 'amb1' ? null : 'amb1'"class="flex-1 flex justify-between items-center focus:outline-none hover:bg-white/50 p-2 rounded-lg transition-all duration-200">
                                                            <span class="text-sm font-bold text-[#555]">Recepção</span>
                                                            <svg class="w-4 h-4 transform transition-transform duration-300"
                                                                :class="{'rotate-180': openAmbiente === 'amb1'}" fill="none"
                                                                stroke="currentColor" viewBox="0 0 24 24"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                            </svg>
                                                        </button>                                                        <a href="detalhesAmbiente.html?mode=view&from=inspection&inspectionId=INSP-001&edificio=edificio1&pavimento=pav1&ambiente=amb1"
                                                            class="ml-3 px-3 py-1 bg-blue-600 text-white text-xs font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
                                                            Ver Detalhes
                                                        </a>
                                                    </div>                                                    <div x-show="openAmbiente === 'amb1'" x-collapse
                                                        class="p-3 border-t border-[#e5e7eb]">
                                                        <p class="text-xs text-gray-600 mb-2">Descrição da Recepção, incluindo seu tamanho, mobiliário e função.</p><!-- Exemplo de Sistema -->                                                        <div
                                                            class="mb-3 rounded-lg border border-[#e8eaed] overflow-hidden shadow-sm">                                                            <div class="flex items-center justify-between p-3 bg-white">
                                                                <button
                                                                    @click="openSistema = openSistema === 'sis1' ? null : 'sis1'"
                                                                    class="flex-1 flex justify-between items-center focus:outline-none hover:bg-white/50 p-2 rounded-lg transition-all duration-200">
                                                                    <span class="text-xs font-bold text-[#777]">Sistema
                                                                        Elétrico</span>
                                                                    <svg class="w-4 h-4 transform transition-transform duration-300"
                                                                        :class="{'rotate-180': openSistema === 'sis1'}"
                                                                        fill="none" stroke="currentColor"
                                                                        viewBox="0 0 24 24"
                                                                        xmlns="http://www.w3.org/2000/svg">
                                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                                            stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                                    </svg>
                                                                </button>                                                                <a href="detalhesSistema.html?mode=view&from=inspection&inspectionId=INSP-001&edificio=edificio1&pavimento=pav1&ambiente=amb1&sistema=sis1"
                                                                    class="ml-2 px-2 py-1 bg-blue-600 text-white text-xs font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
                                                                    Ver Detalhes
                                                                </a>
                                                            </div>                                                            <div x-show="openSistema === 'sis1'" x-collapse
                                                                class="p-3 border-t border-[#e8eaed]">
                                                                <p class="text-xs text-gray-500 mb-2">Descrição geral do sistema elétrico deste ambiente.</p>                                                                <!-- Patologia 1 do Sistema Elétrico -->
                                                                <div class="mb-2 p-2 border border-gray-300 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors shadow-sm">
                                                                    <div class="flex items-center justify-between">
                                                                        <div class="flex-1">
                                                                            <p class="text-sm text-[#333] font-bold mb-1">Patologia: Fiação Exposta</p>
                                                                            <p class="text-xs text-[#637688] mb-1">
                                                                                <strong>Descrição da Patologia:</strong> Fios desencapados
                                                                                próximos ao interruptor principal.
                                                                            </p>
                                                                        </div>
                                                                        <a href="detalhesPatologia.html?mode=view&from=inspection&inspectionId=INSP-001&edificio=edificio1&pavimento=pav1&ambiente=amb1&sistema=sis1&patologia=pat1"
                                                                            class="ml-2 px-2 py-1 bg-red-600 text-white text-xs font-semibold rounded-lg hover:bg-red-700 transition-colors shadow-sm">
                                                                            Ver Patologia
                                                                        </a>
                                                                    </div>
                                                                    <p class="text-xs text-[#637688] font-semibold mb-1">Fotos:</p>
                                                                    <div class="flex gap-2 mt-1 flex-wrap">
                                                                        <img src="/images/icones/disruption.png"
                                                                            alt="Foto da Patologia 1"
                                                                            class="rounded-lg w-20 h-auto object-cover shadow-sm">
                                                                        <img src="/images/icones/disruption.png"
                                                                            alt="Foto da Patologia 2"
                                                                            class="rounded-lg w-20 h-auto object-cover shadow-sm">
                                                                    </div>
                                                                </div><!-- Patologia 2 do Sistema Elétrico (Exemplo) -->                                                                <div class="mb-2 p-2 border border-gray-300 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors shadow-sm">
                                                                    <div class="flex items-center justify-between">
                                                                        <div class="flex-1">
                                                                            <p class="text-sm text-[#333] font-bold mb-1">Patologia: Interruptor Danificado</p>
                                                                            <p class="text-xs text-[#637688] mb-1">
                                                                                <strong>Descrição da Patologia:</strong> Interruptor da sala de estar não funciona corretamente.
                                                                            </p>
                                                                        </div>
                                                                        <a href="detalhesPatologia.html?mode=view&from=inspection&inspectionId=INSP-001&edificio=edificio1&pavimento=pav1&ambiente=amb1&sistema=sis1&patologia=pat2"
                                                                            class="ml-2 px-2 py-1 bg-red-600 text-white text-xs font-semibold rounded-lg hover:bg-red-700 transition-colors shadow-sm">
                                                                            Ver Patologia
                                                                        </a>
                                                                    </div>
                                                                    <p class="text-xs text-[#637688] font-semibold mb-1">Fotos:</p>
                                                                    <div class="flex gap-2 mt-1 flex-wrap">
                                                                        <img src="/images/icones/disruption.png"
                                                                            alt="Foto do Interruptor Danificado"
                                                                            class="rounded-lg w-20 h-auto object-cover shadow-sm">
                                                                    </div>
                                                                </div>
                                                                <!-- Adicionar mais patologias do sistema elétrico aqui -->
                                                            </div>
                                                        </div>                                                        <div
                                                            class="mb-2 rounded-lg border border-[#e8eaed] overflow-hidden shadow-sm">
                                                            <div class="flex items-center justify-between p-3 bg-white">
                                                                <button
                                                                    @click="openSistema = openSistema === 'sis2' ? null : 'sis2'"
                                                                    class="flex-1 flex justify-between items-center focus:outline-none hover:bg-white/50 p-2 rounded-lg transition-all duration-200">
                                                                    <span class="text-xs font-bold text-[#777]">Sistema
                                                                        Hidráulico</span>
                                                                    <svg class="w-4 h-4 transform transition-transform duration-300"
                                                                        :class="{'rotate-180': openSistema === 'sis2'}"
                                                                        fill="none" stroke="currentColor"
                                                                        viewBox="0 0 24 24"
                                                                        xmlns="http://www.w3.org/2000/svg">
                                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                                            stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                                    </svg>
                                                                </button>
                                                                <a href="detalhesSistema.html?mode=view&from=inspection&inspectionId=INSP-001&edificio=edificio1&pavimento=pav1&ambiente=amb1&sistema=sis2"
                                                                    class="ml-2 px-2 py-1 bg-blue-600 text-white text-xs font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
                                                                    Ver Detalhes
                                                                </a>
                                                            </div>
                                                            <div x-show="openSistema === 'sis2'" x-collapse
                                                                class="p-3 border-t border-[#e8eaed]">
                                                                <p class="text-xs text-gray-500 mb-2">Descrição geral do sistema hidráulico deste ambiente.</p><!-- Patologia 1 do Sistema Hidráulico -->                                                                <div class="mb-2 p-2 border border-gray-300 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors shadow-sm">
                                                                    <div class="flex items-center justify-between">
                                                                        <div class="flex-1">
                                                                            <p class="text-sm text-[#333] font-bold mb-1">Patologia: Vazamento</p>
                                                                            <p class="text-xs text-[#637688] mb-1">
                                                                                <strong>Descrição da Patologia:</strong> Gotejamento constante na
                                                                                torneira da pia.
                                                                            </p>
                                                                        </div>
                                                                        <a href="detalhesPatologia.html?mode=view&from=inspection&inspectionId=INSP-001&edificio=edificio1&pavimento=pav1&ambiente=amb1&sistema=sis2&patologia=pat3"
                                                                            class="ml-2 px-2 py-1 bg-red-600 text-white text-xs font-semibold rounded-lg hover:bg-red-700 transition-colors shadow-sm">
                                                                            Ver Patologia
                                                                        </a>
                                                                    </div>
                                                                    <p class="text-xs text-[#637688] font-semibold mb-1">Fotos:</p>
                                                                    <div class="flex gap-2 mt-1 flex-wrap">
                                                                        <img src="/images/icones/disruption.png"
                                                                            alt="Foto da Patologia A"
                                                                            class="rounded-lg w-20 h-auto object-cover shadow-sm">
                                                                    </div>
                                                                </div>
                                                                <!-- Adicionar mais patologias do sistema hidráulico aqui -->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- Adicionar mais ambientes aqui -->
                                            </div>
                                        </div>
                                        <!-- Adicionar mais pavimentos aqui -->
                                    </div>
                                </div>
                                <!-- Adicionar mais edifícios aqui -->
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>    <script>        // Dados das inspeções
        const inspectionsData = {
            "INSP-001": {
                id: "INSP-001",
                name: "Inspeção Edifício Central",
                client: "Edifício Central",
                address: "Rua das Palmeiras, 123, Cidade Exemplo",
                type: "Estrutural",
                status: "Em Andamento",
                creationDate: "01/03/2024",
                startDate: "15/03/2024",
                endDate: "30/03/2024",
                coordinator: "Carlos Silva",
                engineer: "Carlos Silva",
                description: "Inspeção estrutural completa do edifício incluindo fundações, estrutura de concreto e instalações.",
                contactName: "Ana Paula",
                contactEmail: "<EMAIL>",
                contactPhone: "(11) 98765-4321",
                progress: 45,
                inspectors: ["João Kleber", "Maria Fernanda"]
            },
            "INSP-002": {
                id: "INSP-002",
                name: "Inspeção Shopping Plaza",
                client: "Shopping Plaza",
                address: "Avenida Central, 456, Vila Modelo",
                type: "Elétrica",
                status: "Agendada",
                creationDate: "05/03/2024",
                startDate: "10/06/2025",
                endDate: "12/06/2025",
                coordinator: "Maria Santos",
                engineer: "Fernanda Costa",
                description: "Inspeção das instalações elétricas do shopping.",
                contactName: "Pedro Martins",
                contactEmail: "<EMAIL>",
                contactPhone: "(21) 91234-5678",
                progress: 0,
                inspectors: ["Carlos Mendes", "Ana Lima"]
            },
            "INSP-003": {
                id: "INSP-003",
                name: "Inspeção Residencial Aurora",
                client: "Governo Municipal",
                address: "Praça da Matriz, 789, Centro",
                type: "Viaduto",
                status: "Concluída",
                creationDate: "20/02/2024",
                startDate: "15/05/2025",
                endDate: "17/05/2025",
                coordinator: "Ana Lima",
                engineer: "Roberto Almeida",
                description: "Inspeção completa do viaduto municipal incluindo estrutura e fundações.",
                contactName: "Sofia Pereira",
                contactEmail: "<EMAIL>",
                contactPhone: "(31) 99999-8888",
                progress: 100,
                inspectors: ["Fernanda Rocha", "Paulo Santos"]
            }
        };

        function goBack() {
            window.history.back();
        }

        // Carrega os dados da inspeção baseado no ID da URL
        document.addEventListener('DOMContentLoaded', function() {
            const params = new URLSearchParams(window.location.search);
            const inspectionId = params.get('id');
            
            if (inspectionId && inspectionsData[inspectionId]) {
                loadInspectionData(inspectionsData[inspectionId]);
            } else {
                // Se não houver ID ou a inspeção não for encontrada, usa dados padrão
                console.warn('ID da inspeção não encontrado ou inválido, usando dados padrão');
            }
        });        function loadInspectionData(inspection) {
            // Atualiza o título
            const titleElement = document.getElementById('inspection-title-id');
            if (titleElement) {
                titleElement.textContent = `#${inspection.id}`;
            }

            // Atualiza o link de editar para incluir o ID correto
            const editLink = document.querySelector('a[href*="editarInspecao.html"]');
            if (editLink) {
                editLink.href = `editarInspecao.html?id=${inspection.id}`;
            }            // Atualiza os dados nos campos (se existirem elementos específicos)
            updateField('ID da Inspeção:', inspection.id);
            updateField('Nome:', inspection.name);
            updateField('Cliente:', inspection.client);
            updateField('Endereço:', inspection.address);
            updateField('Tipo:', inspection.type);
            updateField('Data de Criação:', inspection.creationDate);
            updateField('Data de Início:', inspection.startDate);
            updateField('Data de Fim:', inspection.endDate || 'Não Definida');
            updateField('Coordenador:', inspection.coordinator);
            updateField('Engenheiro:', inspection.engineer);
            updateField('Descrição:', inspection.description);
            updateField('Nome do Contato:', inspection.contactName);
            updateField('E-mail:', inspection.contactEmail);
            updateField('Telefone:', inspection.contactPhone);

            // Atualiza o status com cores
            const statusBadge = document.getElementById('status-badge');
            if (statusBadge) {
                statusBadge.textContent = inspection.status;
                // Remove classes anteriores
                statusBadge.className = 'px-2 py-1 text-xs font-semibold rounded-full';
                
                if (inspection.status === "Agendada") {
                    statusBadge.classList.add('bg-blue-100', 'text-blue-800');
                } else if (inspection.status === "Em Andamento") {
                    statusBadge.classList.add('bg-yellow-100', 'text-yellow-800');
                } else if (inspection.status === "Concluída") {
                    statusBadge.classList.add('bg-green-100', 'text-green-800');
                } else if (inspection.status === "Cancelada") {
                    statusBadge.classList.add('bg-red-100', 'text-red-800');
                } else {
                    statusBadge.classList.add('bg-gray-100', 'text-gray-600');
                }
            }
        }

        function updateField(label, value) {
            const elements = document.querySelectorAll('p.text-gray-700.text-sm.font-medium');
            elements.forEach(element => {
                if (element.textContent.trim() === label) {
                    const valueElement = element.parentElement.querySelector('p.text-gray-600');
                    if (valueElement) {
                        valueElement.textContent = value;
                    }
                }
            });
        }
    </script>
</body>

</html>