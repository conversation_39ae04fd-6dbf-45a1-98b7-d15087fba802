<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <title>Editar Inspeção</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="dashboard.html">
                        <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>
                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center"> <a href="dashboard.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/botao-de-inicio.png" alt="Painel" width="20px" height="20px">
                            Painel
                        </a>

                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                        <a href="membros.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/grupo.png" alt="Membros" width="20px" height="20px">
                            Membros
                        </a>
                    </div>
                </nav>
                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Inspeção #12350 atribuída à Equipe X.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Relatório da inspeção #12345 pronto.
                                </a>
                                <div x-show="!hasNotifications" class="px-4 py-3 text-sm text-gray-500">
                                    Nenhuma nova notificação.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('../ícones/perfil.png');">
                    </div>
                </div>
            </header>
            <main class="flex-1">
                <div class="px-40 flex flex-1 justify-center py-5">
                    <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
                        <div class="flex flex-wrap justify-between gap-3 p-4">
                            <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight min-w-72">
                                Editar Inspeção</p>
                            <div class="flex gap-x-3">
                                <button onclick="cancelEdit()"
                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-medium leading-normal hover:bg-gray-200">
                                    <span class="truncate">Cancelar</span>
                                </button>
                                <button onclick="saveInspectionChanges()"
                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#007bff] text-white text-sm font-medium leading-normal hover:bg-blue-700">
                                    <span class="truncate">Salvar Alterações</span>
                                </button>
                            </div>
                        </div>
                        <div class="flex flex-col gap-4 p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex flex-col gap-1.5">
                                    <label for="nomeInspecao"
                                        class="text-[#111518] text-sm font-medium leading-normal">Nome da
                                        Inspeção</label>
                                    <input type="text" id="nomeInspecao"
                                        class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <label for="clienteInspecao"
                                        class="text-[#111518] text-sm font-medium leading-normal">Cliente</label>
                                    <input type="text" id="clienteInspecao"
                                        class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                                </div>
                            </div>

                            <div class="flex flex-col gap-1.5">
                                <label for="enderecoInspecao"
                                    class="text-[#111518] text-sm font-medium leading-normal">Endereço</label>
                                <input type="text" id="enderecoInspecao"
                                    class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex flex-col gap-1.5">
                                    <label for="tipoInspecao"
                                        class="text-[#111518] text-sm font-medium leading-normal">Tipo</label>
                                    <select id="tipoInspecao"
                                        class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                                        <option value="">Selecione o tipo</option>
                                        <option value="Estrutural">Estrutural</option>
                                        <option value="Hidráulica">Hidráulica</option>
                                        <option value="Elétrica">Elétrica</option>
                                        <option value="Segurança">Segurança</option>
                                        <option value="Manutenção">Manutenção</option>
                                        <option value="Completa">Completa</option>
                                    </select>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <label for="statusInspecao"
                                        class="text-[#111518] text-sm font-medium leading-normal">Status</label>
                                    <select id="statusInspecao"
                                        class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                                        <option value="Agendada">Agendada</option>
                                        <option value="Em Andamento">Em Andamento</option>
                                        <option value="Concluída">Concluída</option>
                                        <option value="Cancelada">Cancelada</option>
                                    </select>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex flex-col gap-1.5">
                                    <label for="dataInicioInspecao"
                                        class="text-[#111518] text-sm font-medium leading-normal">Data de Início</label>
                                    <input type="date" id="dataInicioInspecao"
                                        class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <label for="dataTerminoInspecao"
                                        class="text-[#111518] text-sm font-medium leading-normal">Data de
                                        Término</label>
                                    <input type="date" id="dataTerminoInspecao"
                                        class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex flex-col gap-1.5">
                                    <label for="coordenadorInspecao"
                                        class="text-[#111518] text-sm font-medium leading-normal">Coordenador</label>
                                    <select id="coordenadorInspecao"
                                        class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                                        <option value="">Selecione o coordenador</option>
                                        <option value="Carlos Silva">Carlos Silva</option>
                                        <option value="Maria Santos">Maria Santos</option>
                                        <option value="Fernanda Lima">Fernanda Lima</option>
                                        <option value="Roberto Souza">Roberto Souza</option>
                                        <option value="Ana Costa">Ana Costa</option>
                                    </select>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <label for="engenheiroInspecao"
                                        class="text-[#111518] text-sm font-medium leading-normal">Engenheiro
                                        Responsável</label>
                                    <select id="engenheiroInspecao"
                                        class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                                        <option value="">Selecione o engenheiro</option>
                                        <option value="Carlos Silva">Carlos Silva</option>
                                        <option value="Maria Santos">Maria Santos</option>
                                        <option value="Fernanda Lima">Fernanda Lima</option>
                                        <option value="Roberto Souza">Roberto Souza</option>
                                        <option value="Ana Costa">Ana Costa</option>
                                    </select>
                                </div>
                            </div>

                            <div class="flex flex-col gap-1.5">
                                <label for="descricaoInspecao"
                                    class="text-[#111518] text-sm font-medium leading-normal">Descrição</label>
                                <textarea id="descricaoInspecao" rows="3"
                                    class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="Descreva o objetivo e escopo da inspeção..."></textarea>
                            </div>

                            <div class="border-t pt-4 mt-4">
                                <h4 class="text-lg font-semibold text-gray-800 mb-3">Informações de Contato</h4>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="flex flex-col gap-1.5">
                                        <label for="nomeContatoInspecao"
                                            class="text-[#111518] text-sm font-medium leading-normal">Nome do
                                            Contato</label>
                                        <input type="text" id="nomeContatoInspecao"
                                            class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                                    </div>
                                    <div class="flex flex-col gap-1.5">
                                        <label for="emailContatoInspecao"
                                            class="text-[#111518] text-sm font-medium leading-normal">Email</label>
                                        <input type="email" id="emailContatoInspecao"
                                            class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                                    </div>
                                    <div class="flex flex-col gap-1.5">
                                        <label for="telefoneContatoInspecao"
                                            class="text-[#111518] text-sm font-medium leading-normal">Telefone</label>
                                        <input type="tel" id="telefoneContatoInspecao"
                                            class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500">
                                    </div>
                                </div>
                            </div>

                            <div class="flex flex-col gap-1.5">
                                <label for="observacoesInspecao"
                                    class="text-[#111518] text-sm font-medium leading-normal">Observações
                                    Adicionais</label>
                                <textarea id="observacoesInspecao" rows="4"
                                    class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="Adicione observações sobre a inspeção..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <script>        const inspectionsData = {
            "INSP-001": {
                id: "INSP-001",
                name: "Inspeção Edifício Central",
                client: "Edifício Central",
                address: "Rua das Palmeiras, 123, Cidade Exemplo",
                type: "Estrutural",
                status: "Em Andamento",
                creationDate: "01/03/2024",
                startDate: "15/03/2024",
                endDate: "30/03/2024",
                coordinator: "Carlos Silva",
                engineer: "Carlos Silva",
                description: "Inspeção estrutural completa do edifício incluindo fundações, estrutura de concreto e instalações.",
                contactName: "Ana Paula",
                contactEmail: "<EMAIL>",
                contactPhone: "(11) 98765-4321",
                progress: 45,
                inspectors: ["João Kleber", "Maria Fernanda"]
            },
            "INSP-002": {
                id: "INSP-002",
                name: "Inspeção Shopping Plaza",
                client: "Shopping Plaza",
                address: "Avenida Central, 456, Vila Modelo",
                type: "Elétrica",
                status: "Agendada",
                creationDate: "05/03/2024",
                startDate: "10/06/2025",
                endDate: "12/06/2025",
                coordinator: "Maria Santos",
                engineer: "Fernanda Costa",
                description: "Inspeção das instalações elétricas do shopping.",
                contactName: "Pedro Martins",
                contactEmail: "<EMAIL>",
                contactPhone: "(21) 91234-5678",
                progress: 0,
                inspectors: ["Carlos Mendes", "Ana Lima"]
            },
            "INSP-003": {
                id: "INSP-003",
                name: "Inspeção Residencial Aurora",
                client: "Governo Municipal",
                address: "Praça da Matriz, 789, Centro",
                type: "Viaduto",
                status: "Concluída",
                creationDate: "20/02/2024",
                startDate: "15/05/2025",
                endDate: "17/05/2025",
                coordinator: "Ana Lima",
                engineer: "Roberto Almeida",
                description: "Inspeção completa do viaduto municipal incluindo estrutura e fundações.",
                contactName: "Sofia Pereira",
                contactEmail: "<EMAIL>",
                contactPhone: "(31) 99999-8888",
                progress: 100,
                inspectors: ["Fernanda Rocha", "Paulo Santos"]
            }
        };

        let currentInspectionId = null;

        document.addEventListener('DOMContentLoaded', function () {
            const params = new URLSearchParams(window.location.search);
            currentInspectionId = params.get('id');
            const inspection = inspectionsData[currentInspectionId];

            if (inspection) {
                populateForm(inspection);
            } else {
                alert('Inspeção não encontrada para edição!');
                window.location.href = 'dashboard.html';
            }
        });

        function populateForm(inspection) {
            // Preencher campos básicos
            document.getElementById('nomeInspecao').value = inspection.name || '';
            document.getElementById('clienteInspecao').value = inspection.client || '';
            document.getElementById('enderecoInspecao').value = inspection.address || '';
            document.getElementById('tipoInspecao').value = inspection.type || '';
            document.getElementById('statusInspecao').value = inspection.status || '';

            // Converter e preencher datas
            document.getElementById('dataInicioInspecao').value = convertDateToISO(inspection.startDate);
            document.getElementById('dataTerminoInspecao').value = convertDateToISO(inspection.endDate);

            // Preencher responsáveis
            document.getElementById('coordenadorInspecao').value = inspection.coordinator || '';
            document.getElementById('engenheiroInspecao').value = inspection.engineer || '';

            // Preencher descrição
            document.getElementById('descricaoInspecao').value = inspection.description || '';

            // Preencher contatos
            document.getElementById('nomeContatoInspecao').value = inspection.contactName || '';
            document.getElementById('emailContatoInspecao').value = inspection.contactEmail || '';
            document.getElementById('telefoneContatoInspecao').value = inspection.contactPhone || '';

            // Preencher observações (usar description se não houver campo específico)
            document.getElementById('observacoesInspecao').value = inspection.notes || '';
        }

        function convertDateToISO(dateString) {
            if (!dateString) return '';
            const parts = dateString.split('/');
            if (parts.length === 3) {
                return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
            }
            return '';
        }

        function convertISOToDate(isoString) {
            if (!isoString) return '';
            const parts = isoString.split('-');
            if (parts.length === 3) {
                return `${parts[2]}/${parts[1]}/${parts[0]}`;
            }
            return '';
        }

        function saveInspectionChanges() {
            if (!currentInspectionId) {
                alert('Erro: ID da inspeção não encontrado.');
                return;
            }

            const inspection = inspectionsData[currentInspectionId];
            if (!inspection) {
                alert('Erro: Inspeção não encontrada.');
                return;
            }

            // Validar campos obrigatórios
            const requiredFields = [
                { id: 'nomeInspecao', name: 'Nome da Inspeção' },
                { id: 'clienteInspecao', name: 'Cliente' },
                { id: 'enderecoInspecao', name: 'Endereço' },
                { id: 'dataInicioInspecao', name: 'Data de Início' }
            ];

            for (const field of requiredFields) {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    alert(`Por favor, preencha o campo: ${field.name}`);
                    element.focus();
                    return;
                }
            }

            // Atualizar dados da inspeção
            inspection.name = document.getElementById('nomeInspecao').value.trim();
            inspection.client = document.getElementById('clienteInspecao').value.trim();
            inspection.address = document.getElementById('enderecoInspecao').value.trim();
            inspection.type = document.getElementById('tipoInspecao').value;
            inspection.status = document.getElementById('statusInspecao').value;
            inspection.startDate = convertISOToDate(document.getElementById('dataInicioInspecao').value);
            inspection.endDate = convertISOToDate(document.getElementById('dataTerminoInspecao').value);
            inspection.coordinator = document.getElementById('coordenadorInspecao').value;
            inspection.engineer = document.getElementById('engenheiroInspecao').value;
            inspection.description = document.getElementById('descricaoInspecao').value.trim();
            inspection.contactName = document.getElementById('nomeContatoInspecao').value.trim();
            inspection.contactEmail = document.getElementById('emailContatoInspecao').value.trim();
            inspection.contactPhone = document.getElementById('telefoneContatoInspecao').value.trim();
            inspection.notes = document.getElementById('observacoesInspecao').value.trim();

            // Validar email se preenchido
            const email = inspection.contactEmail;
            if (email && !isValidEmail(email)) {
                alert('Por favor, insira um email válido.');
                document.getElementById('emailContatoInspecao').focus();
                return;
            }

            // Simular salvamento (em um sistema real, seria feita uma requisição para o servidor)
            try {
                // Aqui seria feita a chamada para a API
                console.log('Dados da inspeção atualizados:', inspection);

                alert('Inspeção atualizada com sucesso!');
                window.location.href = `detalhesInspecao.html?id=${currentInspectionId}`;
            } catch (error) {
                console.error('Erro ao salvar inspeção:', error);
                alert('Erro ao salvar as alterações. Tente novamente.');
            }
        } function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function cancelEdit() {
            if (currentInspectionId) {
                window.location.href = `detalhesInspecao.html?id=${currentInspectionId}`;
            } else {
                window.location.href = 'dashboard.html';
            }
        }
    </script>
</body>

</html>