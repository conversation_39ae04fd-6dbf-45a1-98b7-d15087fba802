const fs = require('fs');
const path = require('path');

// Função para converter arquivos HTML para EJS
function convertHtmlToEjs() {
    const pagesDir = path.join(__dirname, '../views/pages');
    
    // Função recursiva para processar diretórios
    function processDirectory(dir) {
        const items = fs.readdirSync(dir);
        
        items.forEach(item => {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                // Se for diretório, processar recursivamente
                processDirectory(fullPath);
            } else if (path.extname(item) === '.html') {
                // Se for arquivo HTML, converter para EJS
                const ejsPath = fullPath.replace('.html', '.ejs');
                
                // Ler conteúdo do arquivo HTML
                let content = fs.readFileSync(fullPath, 'utf8');
                
                // Fazer ajustes específicos para EJS
                content = adjustContentForEjs(content);
                
                // Escrever arquivo EJS
                fs.writeFileSync(ejsPath, content);
                
                // Remover arquivo HTML original
                fs.unlinkSync(fullPath);
                
                console.log(`Convertido: ${fullPath} → ${ejsPath}`);
            }
        });
    }
    
    processDirectory(pagesDir);
}

// Função para ajustar conteúdo para EJS
function adjustContentForEjs(content) {
    // Remover DOCTYPE e tags html/head/body se existirem (para páginas que serão incluídas em layout)
    // Manter apenas o conteúdo principal
    
    // Ajustar links relativos para rotas do Express
    content = content.replace(/href="([^"]*\.html)"/g, (match, url) => {
        // Converter links HTML para rotas Express
        const route = convertHtmlLinkToRoute(url);
        return `href="${route}"`;
    });
    
    // Ajustar window.location.href para rotas Express
    content = content.replace(/window\.location\.href\s*=\s*['"]([^'"]*\.html)['"]/g, (match, url) => {
        const route = convertHtmlLinkToRoute(url);
        return `window.location.href = '${route}'`;
    });
    
    // Ajustar caminhos de imagens
    content = content.replace(/src="\.\.\/ícones\//g, 'src="/images/icones/');
    
    return content;
}

// Função para converter links HTML para rotas Express
function convertHtmlLinkToRoute(htmlLink) {
    // Remover ../ e .html
    let route = htmlLink.replace(/\.\.\//g, '').replace(/\.html$/, '');
    
    // Mapear rotas específicas
    const routeMap = {
        'login/login': '/login',
        'login/cadastro': '/cadastro',
        'login/recuperarSenha': '/recuperar-senha',
        'admin/dashboard': '/admin/dashboard',
        'admin/membros': '/admin/membros',
        'admin/relatorios': '/admin/relatorios',
        'admin/novaInspecao': '/admin/nova-inspecao',
        'admin/detalhesInspecao': '/admin/detalhes-inspecao',
        'admin/editarInspecao': '/admin/editar-inspecao',
        'admin/visualizarRelatorio': '/admin/visualizar-relatorio',
        'admin/detalhesAmbiente': '/admin/detalhes-ambiente',
        'admin/detalhesEdificio': '/admin/detalhes-edificio',
        'admin/detalhesMembro': '/admin/detalhes-membro',
        'admin/detalhesPatologia': '/admin/detalhes-patologia',
        'admin/detalhesPavimento': '/admin/detalhes-pavimento',
        'admin/detalhesSistema': '/admin/detalhes-sistema',
        'inspetor/listaInspecoes': '/inspetor/lista-inspecoes',
        'inspetor/iniciarInspecao': '/inspetor/iniciar-inspecao',
        'inspetor/perfil': '/inspetor/perfil',
        'inspetor/relatorios': '/inspetor/relatorios',
        'inspetor/detalhesInspecao': '/inspetor/detalhes-inspecao',
        'inspetor/visualizarRelatorio': '/inspetor/visualizar-relatorio',
        'inspetor/detalhesAmbiente': '/inspetor/detalhes-ambiente',
        'inspetor/detalhesEdificio': '/inspetor/detalhes-edificio',
        'inspetor/detalhesPatologia': '/inspetor/detalhes-patologia',
        'inspetor/detalhesPavimento': '/inspetor/detalhes-pavimento',
        'inspetor/detalhesSistema': '/inspetor/detalhes-sistema'
    };
    
    return routeMap[route] || `/${route}`;
}

// Executar conversão
if (require.main === module) {
    console.log('Iniciando conversão de arquivos HTML para EJS...');
    convertHtmlToEjs();
    console.log('Conversão concluída!');
}

module.exports = { convertHtmlToEjs, adjustContentForEjs, convertHtmlLinkToRoute };
