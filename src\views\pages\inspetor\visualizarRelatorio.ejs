<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Relatório - InfraWatch</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <link rel="icon" type="image/x-icon" href="../ícones/disruption.png" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        [x-cloak] {
            display: none !important;
        }

        .nav-item-active {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
            background-color: #eff6ff;
        }

        .report-container-ipt {
            max-width: 17cm;
            margin: 0 auto;
            padding: 1cm;
            background: white;
            border: 1px solid #ccc;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            font-family: Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.5;
            min-height: 25cm;
        }

        .report-header-ipt {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }

        .report-header-ipt .institute-name {
            font-size: 16pt;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .report-header-ipt .report-type {
            font-size: 14pt;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .report-title-ipt {
            text-align: center;
            font-size: 18pt;
            font-weight: bold;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .report-content-ipt h2 {
            font-size: 14pt;
            border-bottom: 1px solid #eee;
            padding-bottom: 3px;
            margin-top: 20px;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .editable-section-title {
            /* Estilo para H2 editável */
            border: 1px dashed #cbd5e0;
            padding: 4px 8px;
            background-color: #f9fafb;
            display: block;
            /* Garante que ocupe a largura e a borda funcione bem */
        }

        .editable-section-title:focus {
            outline: 2px solid #2563eb;
            background-color: white;
            border-color: transparent;
            /* Esconde a borda tracejada ao focar */
        }

        .report-content-ipt h3 {
            font-size: 13pt;
            margin-top: 15px;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .report-content-ipt p,
        .report-content-ipt ul,
        .report-content-ipt li {
            font-size: 12pt;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .report-content-ipt table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            margin-bottom: 15px;
            font-size: 10pt;
        }

        .report-content-ipt th,
        .report-content-ipt td {
            border: 1px solid #666;
            padding: 6px;
            text-align: left;
            vertical-align: top;
        }

        .report-content-ipt th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .report-content-ipt img.pathology-photo {
            max-width: 150px;
            height: auto;
            margin: 5px;
            border: 1px solid #ccc;
            display: inline-block;
        }

        .editable {
            border: 1px dashed #cbd5e0;
            padding: 8px;
            min-height: 50px;
            background-color: #f9fafb;
        }

        .editable-inline {
            border: 1px dashed #cbd5e0;
            padding: 2px 4px;
            background-color: #f9fafb;
            display: inline-block;
            min-width: 100px;
        }

        .editable-summary-item {
            border: 1px dashed #cbd5e0;
            padding: 1px 3px;
            background-color: #f9fafb;
            display: inline-block;
            min-width: 200px;
            margin-bottom: 2px;
        }        .editable:focus,
        .editable-inline:focus,
        .editable-summary-item:focus,
        .editable-section-title:focus {
            outline: 2px solid #1980e6;
            background-color: white;
        }

        .photo-upload-container button {
            font-size: 0.8rem;
            padding: 0.2rem 0.5rem;
        }

        .photo-preview img {
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .action-button {
            background-color: #4a5568;
            color: white;
            padding: 4px 8px;
            font-size: 0.8rem;
            border-radius: 4px;
            margin-left: 8px;
            cursor: pointer;
            border: none;
        }

        .action-button:hover {
            background-color: #2d3748;
        }

        .remove-button {
            background-color: #e53e3e;
        }

        .remove-button:hover {
            background-color: #c53030;
        }

        .add-button {
            background-color: #38a169;
            margin-top: 10px;
            margin-bottom: 10px;
        }

        .add-button:hover {
            background-color: #2f855a;
        }

        .summary-item-controls button {
            font-size: 0.7rem;
            padding: 1px 4px;
            margin-left: 5px;
        }


        @media print {

            body>.group\/design-root>.layout-container>header,
            .no-print,
            .action-button,
            .summary-item-controls {
                display: none !important;
                visibility: hidden !important;
            }

            .editable,
            .editable-inline,
            .editable-summary-item,
            .editable-section-title {
                border: none !important;
                padding: 0 !important;
                min-height: unset !important;
                background-color: transparent !important;
                display: block;
            }

            .editable-inline,
            .editable-summary-item {
                display: inline !important;
            }

            .editable-section-title {
                padding-bottom: 3px !important;
                /* Mantém o padding-bottom do h2 original */
            }

            html,
            body {
                width: auto !important;
                height: auto !important;
                background: white !important;
                margin: 0 !important;
                padding: 0 !important;
                font-size: 12pt;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .layout-container,
            main {
                display: block !important;
                width: auto !important;
                height: auto !important;
                overflow: visible !important;
                margin: 0 !important;
                padding: 0 !important;
                background: transparent !important;
                box-shadow: none !important;
            }

            .print-content {
                box-shadow: none !important;
                background: transparent !important;
                height: auto !important;
                min-height: unset !important;
                overflow: visible !important;
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
                border: none !important;
            }

            .report-container-ipt {
                max-width: 100% !important;
                width: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
                border: none !important;
                box-shadow: none !important;
                background: transparent !important;
                height: auto !important;
                min-height: unset !important;
                overflow: visible !important;
                page-break-after: avoid;
            }

            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
                color: black !important;
            }

            a {
                color: black !important;
                text-decoration: none;
            }

            img.pathology-photo {
                max-width: 200px !important;
            }

            .sumario-list {
                padding-left: 0;
                list-style-type: none;
            }

            .sumario-list .sub-item {
                padding-left: 20px;
            }
        }

        @page {
            margin: 1cm;
        }
    </style>
</head>

<body x-data="reportEditorData()" x-init="init()">
    <div class="relative flex size-full min-h-screen flex-col bg-gray-100 group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] bg-white px-10 py-3 no-print">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="/listaInspecoes">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch</h2>
                    </a>
                </div>                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="/listaInspecoes">Inspeções</a>
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="/relatorios">Relatórios</a>
                    </div>
                </nav>                <div class="flex justify-end gap-8">
                    <a href="/perfil" class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('../ícones/perfil.png');">
                    </a>
                </div>
            </header>

            <main class="flex-1 py-10 bg-gray-100">
                <div class="max-w-7xl mx-auto bg-white p-8 shadow-lg rounded-lg print-content">
                    <div class="flex justify-between items-center mb-6 no-print">
                        <h1 class="text-2xl font-bold text-[#111518]"
                            x-text="`Editando Relatório: ${reportData.reportId || 'Novo Relatório'}`"></h1>
                        <div class="flex items-center gap-4">
                            <a href="/relatorios"
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-medium leading-normal hover:bg-gray-200">
                                Voltar
                            </a>
                            <button @click="saveReport()"
                                class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-full hover:bg-green-700 transition-colors">
                                Salvar Relatório
                            </button>
                            <button onclick="window.print()"
                                class="flex items-center gap-2 px-4 py-2 bg-[#1980e6] text-white text-sm font-medium rounded-full hover:bg-[#1565c0] transition-colors">
                                Imprimir / Salvar PDF
                            </button>
                        </div>
                    </div>

                    <div class="report-container-ipt">
                        <div class="report-header-ipt">
                            <div class="institute-name editable-inline" contenteditable="true"
                                x-html="reportData.instituteName"
                                @blur="reportData.instituteName = $event.target.innerText"></div>
                            <div class="report-type editable-inline" contenteditable="true"
                                x-html="reportData.reportType" @blur="reportData.reportType = $event.target.innerText">
                            </div>
                            <div class="report-title-ipt editable" contenteditable="true" x-html="reportData.title"
                                @blur="reportData.title = $event.target.innerHTML"></div>
                            <div class="responsible-unit editable-inline" contenteditable="true"
                                x-html="reportData.responsibleUnit"
                                @blur="reportData.responsibleUnit = $event.target.innerText"></div>
                            <div class="responsible-lab editable-inline" contenteditable="true"
                                x-html="reportData.responsibleLab"
                                @blur="reportData.responsibleLab = $event.target.innerText"></div>
                        </div>
                        <div class="report-number-top-right"
                            x-text="`Relatório Técnico nº ${reportData.reportId || '(Novo)'}`"></div>
                        <div class="clear-float"></div>

                        <div class="report-content-ipt">
                            <h2 class="editable-section-title" contenteditable="true"
                                x-html="reportData.sectionTitles.summary"
                                @blur="reportData.sectionTitles.summary = $event.target.innerHTML"></h2>
                            <div class="editable" contenteditable="true" x-html="reportData.summary"
                                @blur="reportData.summary = $event.target.innerHTML"></div>
                            <p class="summary-keywords"><strong>Palavras-chave:</strong> <span class="editable-inline"
                                    contenteditable="true" x-html="reportData.keywords"
                                    @blur="reportData.keywords = $event.target.innerText"></span></p>

                            <h2 class="editable-section-title" contenteditable="true"
                                x-html="reportData.sectionTitles.tableOfContents"
                                @blur="reportData.sectionTitles.tableOfContents = $event.target.innerHTML"></h2>
                            <ul class="sumario-list">
                                <template x-for="(item, index) in reportData.summaryItems" :key="item.id">
                                    <li>
                                        <div class="flex items-center">
                                            <span class="editable-summary-item" contenteditable="true"
                                                x-html="item.title" @blur="item.title = $event.target.innerText"></span>
                                            <div class="summary-item-controls no-print">
                                                <button @click="addSummarySubItem(item.id)"
                                                    class="action-button text-xs" title="Adicionar Subitem">+</button>
                                                <button @click="removeSummaryItem(item.id)"
                                                    class="action-button remove-button text-xs"
                                                    title="Remover Item">x</button>
                                            </div>
                                        </div>
                                        <template x-if="item.subItems && item.subItems.length > 0">
                                            <ul class="sumario-list sub-item ml-5">
                                                <template x-for="(subItem, subIndex) in item.subItems"
                                                    :key="subItem.id">
                                                    <li>
                                                        <div class="flex items-center">
                                                            <span class="editable-summary-item" contenteditable="true"
                                                                x-html="subItem.title"
                                                                @blur="subItem.title = $event.target.innerText"></span>
                                                            <div class="summary-item-controls no-print">
                                                                <button @click="removeSummaryItem(item.id, subItem.id)"
                                                                    class="action-button remove-button text-xs"
                                                                    title="Remover Subitem">x</button>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </template>
                                            </ul>
                                        </template>
                                    </li>
                                </template>
                            </ul>
                            <button @click="addSummaryItem()" class="action-button add-button no-print">Adicionar Tópico
                                ao Sumário</button>

                            <h2 class="editable-section-title" contenteditable="true"
                                x-html="reportData.sectionTitles.introduction"
                                @blur="reportData.sectionTitles.introduction = $event.target.innerHTML"></h2>
                            <div class="editable" contenteditable="true" x-html="reportData.introduction"
                                @blur="reportData.introduction = $event.target.innerHTML"></div>

                            <h2 class="editable-section-title" contenteditable="true"
                                x-html="reportData.sectionTitles.objective"
                                @blur="reportData.sectionTitles.objective = $event.target.innerHTML"></h2>
                            <div class="editable" contenteditable="true" x-html="reportData.objective"
                                @blur="reportData.objective = $event.target.innerHTML"></div>

                            <h2 class="editable-section-title" contenteditable="true"
                                x-html="reportData.sectionTitles.method"
                                @blur="reportData.sectionTitles.method = $event.target.innerHTML"></h2>
                            <div class="editable" contenteditable="true" x-html="reportData.method"
                                @blur="reportData.method = $event.target.innerHTML"></div>

                            <h2 class="editable-section-title" contenteditable="true"
                                x-html="reportData.sectionTitles.pathologies"
                                @blur="reportData.sectionTitles.pathologies = $event.target.innerHTML"></h2>
                            <template x-if="reportData.pathologies.length === 0">
                                <p>Nenhuma patologia carregada ou adicionada.</p>
                            </template>
                            <template x-for="(pathology, index) in reportData.pathologies" :key="pathology.id">
                                <div class="border p-3 my-3 rounded">
                                    <div class="flex justify-between items-center mb-2">
                                        <h3 class="editable-inline" contenteditable="true"
                                            x-html="pathology.customTitle || `4.${index + 1} Patologia em ${pathology.ambiente || 'Local não especificado'}`"
                                            @blur="pathology.customTitle = $event.target.innerText"></h3>
                                        <button @click="removePathology(pathology.id)"
                                            class="action-button remove-button">Remover</button>
                                    </div>
                                    <p><strong>Localização (Edifício/Pavimento/Ambiente/Sistema):</strong></p>
                                    <div class="grid grid-cols-2 gap-2 mb-2">
                                        <span class="editable-inline w-full" contenteditable="true"
                                            x-html="pathology.edificio"
                                            @blur="pathology.edificio = $event.target.innerText"></span>
                                        <span class="editable-inline w-full" contenteditable="true"
                                            x-html="pathology.pavimento"
                                            @blur="pathology.pavimento = $event.target.innerText"></span>
                                        <span class="editable-inline w-full" contenteditable="true"
                                            x-html="pathology.ambiente"
                                            @blur="pathology.ambiente = $event.target.innerText"></span>
                                        <span class="editable-inline w-full" contenteditable="true"
                                            x-html="pathology.sistema"
                                            @blur="pathology.sistema = $event.target.innerText"></span>
                                    </div>
                                    <p><strong>Constatação:</strong></p>
                                    <div class="editable" contenteditable="true" x-html="pathology.finding"
                                        @blur="pathology.finding = $event.target.innerHTML"></div>
                                    <p><strong>Recomendação:</strong></p>
                                    <div class="editable" contenteditable="true" x-html="pathology.recommendation"
                                        @blur="pathology.recommendation = $event.target.innerHTML"></div>
                                    <div class="photo-upload-container mt-2 mb-4">
                                        <p><strong>Fotos:</strong></p>
                                        <div class="photo-preview flex flex-wrap items-center">
                                            <template x-for="(photo, photoIndex) in pathology.photos" :key="photoIndex">
                                                <div class="relative mr-2 mb-2">
                                                    <img :src="photo.preview" :alt="photo.file" class="pathology-photo">
                                                    <button @click="removePhotoFromPathology(pathology.id, photoIndex)"
                                                        class="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 text-xs leading-none">&times;</button>
                                                </div>
                                            </template>
                                        </div>
                                        <input type="file" @change="addPhotoToPathology(pathology.id, $event)"
                                            accept="image/*" class="text-sm">
                                    </div>
                                </div>
                            </template>
                            <button @click="addPathology()" class="action-button add-button">Adicionar
                                Patologia</button>

                            <h2 class="editable-section-title" contenteditable="true"
                                x-html="reportData.sectionTitles.pathologiesTable"
                                @blur="reportData.sectionTitles.pathologiesTable = $event.target.innerHTML"></h2>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Item Ref.</th>
                                        <th>Patologia Identificada</th>
                                        <th>Localização</th>
                                        <th>Classificação de Risco</th>
                                        <th>Prazo Recomendado para Reparo</th>
                                        <th class="no-print">Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template x-if="reportData.pathologiesSummary.length === 0">
                                        <tr>
                                            <td colspan="6" class="text-center py-4">Nenhuma patologia resumida.</td>
                                        </tr>
                                    </template>
                                    <template x-for="(pathologyItem, index) in reportData.pathologiesSummary" :key="pathologyItem.id">
                                        <tr>
                                            <td><input type="text" x-model="pathologyItem.ref" class="w-full p-1 border rounded text-sm"></td>
                                            <td><textarea x-model="pathologyItem.pathology" class="w-full p-1 border rounded text-sm" rows="2"></textarea></td>
                                            <td><input type="text" x-model="pathologyItem.location" class="w-full p-1 border rounded text-sm"></td>
                                            <td>
                                                <select x-model="pathologyItem.risk" @change="pathologyItem.deadline = getDeadlineByRisk($event.target.value)" class="w-full p-1 border rounded text-sm bg-white">
                                                    <option value="Baixo">Baixo</option>
                                                    <option value="Médio">Médio</option>
                                                    <option value="Alto">Alto</option>
                                                </select>
                                            </td>
                                            <td><input type="text" x-model="pathologyItem.deadline" class="w-full p-1 border rounded text-sm"></td>
                                            <td class="no-print">
                                                <button @click="removePathologyFromSummary(pathologyItem.id)" class="action-button remove-button">Remover</button>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                            <button @click="addPathologyToSummary()" class="action-button add-button no-print">Adicionar
                                Patologia ao Resumo</button>
                            <p class="mt-4"><strong>Classificação de Risco:</strong> (Padrão)</p>
                            <ul>
                                <li><strong>Baixo:</strong> <span class="editable-inline" contenteditable="true" x-html="reportData.riskClassification.low" @blur="reportData.riskClassification.low = $event.target.innerText"></span></li>
                                <li><strong>Médio:</strong> <span class="editable-inline" contenteditable="true" x-html="reportData.riskClassification.medium" @blur="reportData.riskClassification.medium = $event.target.innerText"></span></li>
                                <li><strong>Alto:</strong> <span class="editable-inline" contenteditable="true" x-html="reportData.riskClassification.high" @blur="reportData.riskClassification.high = $event.target.innerText"></span></li>
                            </ul>

                            <h2 class="editable-section-title" contenteditable="true"
                                x-html="reportData.sectionTitles.finalConsiderations"
                                @blur="reportData.sectionTitles.finalConsiderations = $event.target.innerHTML"></h2>
                            <div class="editable" contenteditable="true" x-html="reportData.finalConsiderations"
                                @blur="reportData.finalConsiderations = $event.target.innerHTML"></div>

                            <h2 class="editable-section-title" contenteditable="true"
                                x-html="reportData.sectionTitles.technicalTeam"
                                @blur="reportData.sectionTitles.technicalTeam = $event.target.innerHTML"></h2>
                            <div class="final-signature">
                                <p><strong>Coordenador da Inspeção / Inspetor Responsável:</strong></p>
                                <p class="editable-inline" contenteditable="true"
                                    x-html="reportData.technicalTeam.coordinator"
                                    @blur="reportData.technicalTeam.coordinator = $event.target.innerText"></p>
                                <p>CREA: <span class="editable-inline" contenteditable="true"
                                        x-html="reportData.technicalTeam.crea"
                                        @blur="reportData.technicalTeam.crea = $event.target.innerText"></span></p>
                                <br>
                                <p><strong>Inspetor(a) Auxiliar (se houver):</strong></p>
                                <p class="editable-inline" contenteditable="true"
                                    x-html="reportData.technicalTeam.auxiliary"
                                    @blur="reportData.technicalTeam.auxiliary = $event.target.innerText"></p>
                                <p>Qualificação: <span class="editable-inline" contenteditable="true"
                                        x-html="reportData.technicalTeam.qualificationAux"
                                        @blur="reportData.technicalTeam.qualificationAux = $event.target.innerText"></span>
                                </p>
                            </div>
                            <br>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <script>
        function reportEditorData() {
            return {
                user: { role: 'coordenador' },
                reportData: {
                    inspectionId: null, reportId: null,
                    instituteName: 'InfraWatch', reportType: 'RELATÓRIO TÉCNICO',
                    title: 'Título da Inspeção Técnica: [A SER PREENCHIDO]',
                    responsibleUnit: 'Departamento de Engenharia Diagnóstica', responsibleLab: 'N/A',
                    sectionTitles: { 
                        summary: 'RESUMO',
                        tableOfContents: 'SUMÁRIO',
                        introduction: '1 INTRODUÇÃO',
                        objective: '2 OBJETIVO',
                        method: '3 MÉTODO',
                        pathologies: '4 IDENTIFICAÇÃO DOS PROBLEMAS PATOLÓGICOS',
                        pathologiesTable: 'QUADRO 1 - RESUMO DAS PATOLOGIAS E NÍVEIS DE CRITICIDADE', 
                        finalConsiderations: '5 CONSIDERAÇÕES FINAIS',
                        technicalTeam: '6 EQUIPE TÉCNICA'
                    },
                    summary: '[RESUMO A SER PREENCHIDO PELO USUÁRIO]',
                    keywords: '[PALAVRAS-CHAVE A SEREM PREENCHIDAS PELO USUÁRIO]',
                    summaryItems: [
                        { id: 'sum-1', title: '1 INTRODUÇÃO', subItems: [] }, { id: 'sum-2', title: '2 OBJETIVO', subItems: [] },
                        { id: 'sum-3', title: '3 MÉTODO', subItems: [] }, { id: 'sum-4', title: '4 IDENTIFICAÇÃO DOS PROBLEMAS PATOLÓGICOS', subItems: [] },
                        { id: 'sum-5', title: 'QUADRO 1 - RESUMO DAS ANOMALIAS', subItems: [] }, { id: 'sum-6', title: '5 CONSIDERAÇÕES FINAIS', subItems: [] },
                        { id: 'sum-7', title: '6 EQUIPE TÉCNICA', subItems: [] },
                    ],
                    introduction: '[INTRODUÇÃO A SER PREENCHIDA PELO USUÁRIO]',
                    objective: '[OBJETIVO A SER PREENCHIDO PELO USUÁRIO]',
                    method: '[MÉTODO A SER PREENCHIDO PELO USUÁRIO]',
                    pathologies: [], 
                    pathologiesSummary: [], 
                    riskClassification: { 
                        low: 'Impacto mínimo, sem risco imediato à segurança ou funcionalidade. Recomenda-se monitoramento e intervenção em médio a longo prazo.',
                        medium: 'Impacto moderado, pode afetar a funcionalidade ou durabilidade em médio prazo. Recomenda-se intervenção em curto a médio prazo.',
                        high: 'Impacto significativo, risco à segurança, funcionalidade ou grande perda de desempenho. Recomenda-se intervenção imediata ou em curto prazo.'
                    },
                    finalConsiderations: '[CONSIDERAÇÕES FINAIS A SEREM PREENCHIDAS PELO USUÁRIO]',
                    technicalTeam: {
                        coordinator: '[NOME DO COORDENADOR / RESPONSÁVEL]', crea: '[CREA]',
                        auxiliary: '[NOME DO INSPETOR AUXILIAR (SE HOUVER)]', qualificationAux: '[QUALIFICAÇÃO]'
                    }
                },
                inspectionsDataStore: {
                    "INSP-001": {
                        name: "Inspeção Edifício Alfa",
                        address: "Rua das Palmeiras, 123, Cidade Exemplo",
                        edificios: [
                            {
                                id: "EDF-001", nome: "Edifício Principal", tipo: "Comercial",
                                pavimentos: [
                                    {
                                        id: "PAV-E01-01", nome: "Térreo", numero: 0,
                                        ambientes: [
                                            {
                                                id: "AMB-E01-01-01", nome: "Recepção", tipo: "Comum",
                                                sistemas: [
                                                    {
                                                        id: "SIS-E01-01-01-01", nome: "Iluminação", tipo: "Elétrico",
                                                        patologias: [{ id: "PAT-001", descricao: "Lâmpada queimada", criticidade: "Baixa", observacoes: "Substituir.", fotos: [] }]
                                                    }
                                                ]
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    "INSP-003": {
                        name: "Inspeção Edifício Exemplo",
                        address: "Praça da Matriz, 789",
                        edificios: [
                            {
                                id: "EDF-X01", nome: "Bloco Único", tipo: "Residencial",
                                pavimentos: [
                                    {
                                        id: "PAV-X01-01", nome: "Térreo", numero: 0,
                                        ambientes: [
                                            {
                                                id: "AMB-X01-01-01", nome: "Sala de Estar", tipo: "Comum",
                                                sistemas: [{ id: "SIS-X01", nome: "Alvenaria", tipo: "Vedações", patologias: [{ id: "PAT-X01", descricao: "Fissuras em paredes internas", criticidade: "Baixo", fotos: [] }] }]
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                },
                currentInspectionDetails: null,
                nextPathologyId: 1, nextPathologySummaryId: 1, nextSummaryItemId: 8, 

                init() {
                    const params = new URLSearchParams(window.location.search);
                    this.reportData.inspectionId = params.get('id');
                    this.reportData.reportId = params.get('relId');
                    const reportTitleParam = decodeURIComponent(params.get('titulo') || '');

                    if (reportTitleParam) this.reportData.title = `Título da Inspeção Técnica: ${reportTitleParam}`;
                    else if (this.reportData.inspectionId) this.reportData.title = `Relatório da Inspeção: ${this.reportData.inspectionId}`;

                    if (this.reportData.inspectionId) this.loadInspectionDetails(this.reportData.inspectionId);

                    this.nextPathologyId = this.reportData.pathologies.length > 0 ? Math.max(...this.reportData.pathologies.map(p => parseInt(p.id.split('-').pop()) || 0)) + 1 : 1;
                    this.nextPathologySummaryId = this.reportData.pathologiesSummary.length > 0 ? Math.max(...this.reportData.pathologiesSummary.map(a => parseInt(a.id.split('-').pop()) || 0)) + 1 : 1; 
                    this.nextSummaryItemId = this.reportData.summaryItems.length > 0 ? Math.max(...this.reportData.summaryItems.map(s => parseInt(s.id.split('-').pop()) || 0)) + 1 : 1;

                },
                loadInspectionDetails(inspectionId) {
                    this.currentInspectionDetails = this.inspectionsDataStore[inspectionId];
                    if (this.currentInspectionDetails) {
                        if (!this.reportData.title.includes(this.currentInspectionDetails.name) && this.currentInspectionDetails.name) {
                            this.reportData.title = `Relatório da Inspeção: ${this.currentInspectionDetails.name}`;
                        }
                        this.populatePathologiesFromInspection();
                    } else {
                        this.reportData.pathologies = [];
                        this.reportData.pathologiesSummary = []; 
                    }
                },

                populatePathologiesFromInspection() {
                    if (!this.currentInspectionDetails || !this.currentInspectionDetails.edificios) {
                        this.reportData.pathologies = []; this.reportData.pathologiesSummary = []; return; 
                    }
                    let newPathologies = []; let newPathologiesSummary = []; 
                    this.nextPathologyId = 1; this.nextPathologySummaryId = 1; 

                    this.currentInspectionDetails.edificios.forEach(edificio => {
                        (edificio.pavimentos || []).forEach(pavimento => {
                            (pavimento.ambientes || []).forEach(ambiente => {
                                (ambiente.sistemas || []).forEach(sistema => {
                                    (sistema.patologias || []).forEach(patologia => {
                                        const pathologyId = `insp-${this.nextPathologyId++}`;
                                        const pathologySummaryId = `insp-sum-${this.nextPathologySummaryId++}`; 
                                        newPathologies.push({
                                            id: pathologyId,
                                            customTitle: `Patologia em ${ambiente.nome}`,
                                            edificio: edificio.nome, pavimento: pavimento.nome, ambiente: ambiente.nome, sistema: sistema.nome,
                                            finding: patologia.descricao || '[Descreva a constatação aqui]',
                                            recommendation: patologia.observacoes || '[Descreva a recomendação aqui]',
                                            photos: (patologia.fotos || []).map(f => ({ preview: f.url || f.preview || '../ícones/disruption.png', file: f.name || 'imagem.png' })),
                                            criticidade: patologia.criticidade || 'Baixo', status: patologia.status || 'Pendente'
                                        });
                                        newPathologiesSummary.push({ 
                                            id: pathologySummaryId, 
                                            ref: `4.${this.nextPathologyId - 1}`,
                                            pathology: patologia.descricao || 'Patologia não descrita', 
                                            location: `${edificio.nome}, ${pavimento.nome}, ${ambiente.nome} (${sistema.nome})`,
                                            risk: patologia.criticidade || 'Baixo',
                                            deadline: this.getDeadlineByRisk(patologia.criticidade || 'Baixo')
                                        });
                                    });
                                });
                            });
                        });
                    });
                    this.reportData.pathologies = newPathologies; 
                    this.reportData.pathologiesSummary = newPathologiesSummary; 
                },

                getDeadlineByRisk(risk) {
                    switch (String(risk).toLowerCase()) {
                        case 'alto': return '30 dias';
                        case 'médio': return '90 dias';
                        case 'baixa': return '180 dias';
                        default: return 'A definir';
                    }
                },

                addPathology() {
                    const newId = `manual-${this.nextPathologyId++}`;
                    this.reportData.pathologies.push({
                        id: newId,
                        customTitle: `Nova Patologia ${this.nextPathologyId - 1}`,
                        edificio: '[Edifício]', pavimento: '[Pavimento]', ambiente: '[Ambiente]', sistema: '[Sistema]',
                        finding: '[Nova Constatação]', recommendation: '[Nova Recomendação]', photos: [],
                        criticidade: 'Baixo', status: 'Pendente'
                    });
                },
                removePathology(id) {
                    this.reportData.pathologies = this.reportData.pathologies.filter(p => p.id !== id);
                },

                addPathologyToSummary() { 
                    const newId = `manual-sum-${this.nextPathologySummaryId++}`; 
                    this.reportData.pathologiesSummary.push({ 
                        id: newId, ref: `Item ${this.nextPathologySummaryId - 1}`,
                        pathology: '[Nova Patologia]', location: '[Localização]', risk: 'Baixo', deadline: '[Prazo]' 
                    });
                },
                removePathologyFromSummary(id) { 
                    this.reportData.pathologiesSummary = this.reportData.pathologiesSummary.filter(a => a.id !== id); 
                },

                addPhotoToPathology(pathologyId, event) {
                    const file = event.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const pathology = this.reportData.pathologies.find(p => p.id === pathologyId);
                            if (pathology) {
                                if (!pathology.photos) pathology.photos = [];
                                pathology.photos.push({ preview: e.target.result, file: file.name });
                            }
                        };
                        reader.readAsDataURL(file);
                        event.target.value = null; 
                    }
                },
                removePhotoFromPathology(pathologyId, photoIndex) {
                    const pathology = this.reportData.pathologies.find(p => p.id === pathologyId);
                    if (pathology && pathology.photos) {
                        pathology.photos.splice(photoIndex, 1);
                    }
                },

                
                addSummaryItem() {
                    const newId = `sum-${this.nextSummaryItemId++}`;
                    this.reportData.summaryItems.push({ id: newId, title: `Novo Tópico ${this.nextSummaryItemId - 1}`, subItems: [] });
                },
                addSummarySubItem(parentId) {
                    const parentItem = this.reportData.summaryItems.find(item => item.id === parentId);
                    if (parentItem) {
                        if (!parentItem.subItems) parentItem.subItems = [];
                        const newSubId = `${parentId}-sub-${parentItem.subItems.length + 1}`;
                        parentItem.subItems.push({ id: newSubId, title: 'Novo Subtópico' });
                    }
                },
                removeSummaryItem(itemId, subItemId = null) {
                    if (subItemId) {
                        const parentItem = this.reportData.summaryItems.find(item => item.id === itemId);
                        if (parentItem && parentItem.subItems) {
                            parentItem.subItems = parentItem.subItems.filter(sub => sub.id !== subItemId);
                        }
                    } else {
                        this.reportData.summaryItems = this.reportData.summaryItems.filter(item => item.id !== itemId);
                    }
                },

                saveReport() {
                    console.log("Salvando Relatório:", JSON.parse(JSON.stringify(this.reportData)));
                    alert("Relatório salvo (simulado)! Verifique o console para os dados.");
                }
            }
        }
    </script>
</body>

</html>