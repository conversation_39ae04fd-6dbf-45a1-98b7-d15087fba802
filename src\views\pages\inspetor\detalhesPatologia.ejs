<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes da Patologia - Inspetor</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/disruption.png" />
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="/listaInspecoes">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>
                  <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="/listaInspecoes">Inspeções</a>
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="/relatorios">Relatórios</a>
                    </div>
                </nav>

                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Inspeção INSP-001 atribuída.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Prazo da inspeção INSP-002 se aproximando.
                                </a>
                            </div>
                        </div>
                    </div>                    <a href="/perfil" class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('../ícones/perfil.png');">
                    </a>
                </div>
            </header>

            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1" x-data="pathologyDetails()">
                    <!-- Cabeçalho da Patologia -->
                    <div class="flex flex-wrap justify-between gap-3 p-4">
                        <div class="flex min-w-72 flex-col gap-3">
                            <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight">
                                <span x-text="pathology.name"></span>
                            </p>
                            <p class="text-[#637588] text-sm font-normal leading-normal">
                                ID: <span x-text="pathology.id"></span> | 
                                Severidade: <span class="inline-block px-2 py-1 rounded text-xs font-medium"
                                      :class="getSeverityColor(pathology.severity)" 
                                      x-text="pathology.severity"></span> | 
                                Status: <span class="inline-block px-2 py-1 rounded text-xs font-medium"
                                      :class="getStatusColor(pathology.status)" 
                                      x-text="pathology.status"></span>
                            </p>
                        </div>                        <div class="flex gap-3">
                            <button @click="toggleEditMode()" 
                                    :disabled="isLoading"
                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#1980e6] text-white text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#1565c0] disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                                <svg x-show="isLoading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span x-text="isLoading ? 'Salvando...' : (editMode ? 'Salvar' : 'Editar')"></span>
                            </button>
                        </div>
                    </div>                    <!-- Navegação Breadcrumb -->
                    <div class="px-4 py-2 text-sm text-gray-600">
                        <a :href="`detalhesInspecao.html?id=${inspectionId}`" class="text-blue-600 hover:text-blue-800" x-text="`Inspeção ${inspectionId}`"></a>
                        <span class="mx-2">></span>
                        <a :href="`detalhesEdificio.html?id=${buildingId}&inspection=${inspectionId}`" class="text-blue-600 hover:text-blue-800" x-text="`Edifício ${buildingId}`"></a>
                        <span class="mx-2">></span>
                        <a :href="`detalhesPavimento.html?id=${floorId}&building=${buildingId}&inspection=${inspectionId}`" class="text-blue-600 hover:text-blue-800" x-text="`Pavimento ${floorId}`"></a>
                        <span class="mx-2">></span>
                        <a :href="`detalhesAmbiente.html?id=${environmentId}&floor=${floorId}&building=${buildingId}&inspection=${inspectionId}`" class="text-blue-600 hover:text-blue-800" x-text="`Ambiente ${environmentId}`"></a>
                        <span class="mx-2">></span>
                        <a :href="`detalhesSistema.html?id=${systemId}&environment=${environmentId}&floor=${floorId}&building=${buildingId}&inspection=${inspectionId}`" class="text-blue-600 hover:text-blue-800" x-text="`Sistema ${systemId}`"></a>
                        <span class="mx-2">></span>
                        <span x-text="pathology.name"></span>
                    </div>                    <!-- Informações Básicas -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 px-4 py-4">
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Nome da Patologia <span class="text-red-500">*</span></p>
                            <input x-model="pathology.name" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   type="text"
                                   required>
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Tipo <span class="text-red-500">*</span></p>
                            <select x-model="pathology.type" 
                                    :disabled="!editMode"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    required>
                                <option value="">Selecione o tipo</option>
                                <option value="Estrutural">Estrutural</option>
                                <option value="Elétrica">Elétrica</option>
                                <option value="Hidráulica">Hidráulica</option>
                                <option value="Revestimento">Revestimento</option>
                                <option value="Infiltração">Infiltração</option>
                                <option value="Corrosão">Corrosão</option>
                                <option value="Desgaste">Desgaste</option>
                                <option value="Outros">Outros</option>
                            </select>
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Severidade <span class="text-red-500">*</span></p>
                            <select x-model="pathology.severity" 
                                    :disabled="!editMode"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    required>
                                <option value="">Selecione a severidade</option>
                                <option value="Baixa">Baixa</option>
                                <option value="Média">Média</option>
                                <option value="Alta">Alta</option>
                                <option value="Crítica">Crítica</option>
                            </select>
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Status <span class="text-red-500">*</span></p>
                            <select x-model="pathology.status" 
                                    :disabled="!editMode"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    required>
                                <option value="">Selecione o status</option>
                                <option value="Identificada">Identificada</option>
                                <option value="Em Análise">Em Análise</option>
                                <option value="Em Correção">Em Correção</option>
                                <option value="Corrigida">Corrigida</option>
                                <option value="Monitoramento">Monitoramento</option>
                            </select>
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Data de Identificação <span class="text-red-500">*</span></p>
                            <input x-model="pathology.identification_date" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   type="date"
                                   required>
                        </div>
                        <div class="flex flex-col gap-1">
                            <p class="text-[#111518] text-base font-medium leading-normal">Prazo para Correção</p>
                            <input x-model="pathology.correction_deadline" 
                                   :disabled="!editMode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   type="date">
                        </div>
                    </div>                    <!-- Localização -->
                    <div class="px-4 py-4">
                        <h3 class="text-[#111518] text-lg font-bold leading-tight mb-4">Localização</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="flex flex-col gap-1">
                                <p class="text-[#111518] text-base font-medium leading-normal">Ambiente</p>
                                <input x-model="pathology.location.environment" 
                                       :disabled="!editMode"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       type="text" placeholder="Ex: Sala de Reuniões">
                            </div>
                            <div class="flex flex-col gap-1">
                                <p class="text-[#111518] text-base font-medium leading-normal">Pavimento</p>
                                <input x-model="pathology.location.floor" 
                                       :disabled="!editMode"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       type="text" placeholder="Ex: 2º Andar">
                            </div>
                            <div class="flex flex-col gap-1 md:col-span-2">
                                <p class="text-[#111518] text-base font-medium leading-normal">Localização Específica</p>
                                <textarea x-model="pathology.location.reference" 
                                       :disabled="!editMode"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       rows="2"
                                       placeholder="Descreva a localização específica da patologia (ex: Parede norte próxima à janela, canto superior direito da sala, etc.)"></textarea>
                            </div>
                        </div>
                    </div><!-- Descrição -->
                    <div class="px-4 py-4">
                        <p class="text-[#111518] text-base font-medium leading-normal mb-2">Descrição <span class="text-red-500">*</span></p>
                        <textarea x-model="pathology.description" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  rows="4"
                                  placeholder="Descrição detalhada da patologia..."
                                  required></textarea>
                    </div>

                    <!-- Possíveis Causas -->
                    <div class="px-4 py-4">
                        <p class="text-[#111518] text-base font-medium leading-normal mb-2">Possíveis Causas</p>
                        <textarea x-model="pathology.possible_causes" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  rows="3"
                                  placeholder="Possíveis causas da patologia..."></textarea>
                    </div>

                    <!-- Recomendações -->
                    <div class="px-4 py-4">
                        <p class="text-[#111518] text-base font-medium leading-normal mb-2">Recomendações</p>
                        <textarea x-model="pathology.recommendations" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  rows="3"
                                  placeholder="Recomendações para correção..."></textarea>
                    </div>

                    <!-- Observações -->
                    <div class="px-4 py-4">
                        <p class="text-[#111518] text-base font-medium leading-normal mb-2">Observações</p>
                        <textarea x-model="pathology.observations" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  rows="3"
                                  placeholder="Observações adicionais..."></textarea>
                    </div>                    <!-- Fotos da Patologia -->
                    <div class="px-4 py-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-[#111518] text-lg font-bold leading-tight">Fotos da Patologia</h3>
                            <button x-show="editMode" @click="addPhoto()" 
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                                <svg class="w-4 h-4 inline-block mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                                </svg>
                                Adicionar Foto
                            </button>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" x-show="pathology.photos && pathology.photos.length > 0">
                            <template x-for="(photo, index) in pathology.photos" :key="index">
                                <div class="relative group bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                                    <img :src="photo.url" :alt="photo.description" 
                                         class="w-full h-48 object-cover cursor-pointer"
                                         @click="viewPhoto(photo)">
                                    <div x-show="editMode" class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                        <button @click="removePhoto(index)" 
                                                class="bg-red-500 text-white rounded-full p-2 hover:bg-red-600 transition-colors duration-200 shadow-lg">
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="p-3">
                                        <p class="text-sm text-gray-600 line-clamp-2" x-text="photo.description || 'Sem descrição'"></p>
                                        <span class="inline-block mt-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full" x-text="photo.photo_type || 'Geral'"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div x-show="!pathology.photos || pathology.photos.length === 0" 
                             class="text-center py-12 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                            <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                            <p class="text-lg font-medium mb-2">Nenhuma foto adicionada</p>
                            <p class="text-sm">Clique em "Adicionar Foto" para inserir imagens da patologia</p>
                        </div>
                    </div>                    <!-- Histórico de Alterações -->
                    <div class="px-4 py-4">
                        <h3 class="text-[#111518] text-lg font-bold leading-tight mb-4">Histórico de Alterações</h3>
                        <div class="space-y-3" x-show="pathology.history && pathology.history.length > 0">
                            <template x-for="change in pathology.history" :key="change.id">
                                <div class="border border-gray-200 rounded-lg p-4 bg-white hover:shadow-sm transition-shadow duration-200">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-gray-900 mb-1" x-text="change.description"></p>
                                            <p class="text-xs text-gray-500 mb-2" x-text="`${change.date} - ${change.user}`"></p>
                                            <div x-show="change.details" class="text-xs text-gray-600 bg-gray-50 p-2 rounded" x-text="change.details"></div>
                                        </div>
                                        <span class="inline-block px-3 py-1 rounded-full text-xs font-medium ml-3"
                                              :class="getStatusColor(change.status)" 
                                              x-text="change.status"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div x-show="!pathology.history || pathology.history.length === 0" 
                             class="text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
                            <svg class="w-8 h-8 mx-auto mb-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <p>Nenhuma alteração registrada</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para visualizar/editar fotos -->
    <div x-show="showPhotoModal" @click.away="closePhotoModal()" 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-cloak>
        <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">Foto da Patologia</h3>
                <button @click="closePhotoModal()" class="text-gray-500 hover:text-gray-700">
                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div x-show="currentPhoto">
                <img :src="currentPhoto?.url" :alt="currentPhoto?.description" 
                     class="w-full max-h-96 object-contain mb-4">
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium mb-1">Descrição</label>
                        <textarea x-model="currentPhoto.description" 
                                  :disabled="!editMode"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                                  rows="3"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Tipo de Foto</label>
                        <select x-model="currentPhoto.photo_type" 
                                :disabled="!editMode"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100">
                            <option value="Geral">Vista Geral</option>
                            <option value="Detalhe">Detalhe</option>
                            <option value="Antes">Antes da Correção</option>
                            <option value="Depois">Depois da Correção</option>
                        </select>
                    </div>
                    <div x-show="editMode" class="flex gap-3">
                        <button @click="savePhotoDescription()" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Salvar
                        </button>
                        <button @click="closePhotoModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Cancelar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>    <script>
        function pathologyDetails() {
            return {
                editMode: false,
                showPhotoModal: false,
                currentPhoto: null,
                inspectionId: '',
                buildingId: '',
                floorId: '',
                environmentId: '',
                systemId: '',
                isLoading: false,
                pathology: {
                    id: '',
                    name: '',
                    type: '',
                    severity: '',
                    status: '',
                    identification_date: '',
                    correction_deadline: '',
                    location: {
                        x: 0,
                        y: 0,
                        height: 0,
                        reference: ''
                    },
                    description: '',
                    possible_causes: '',
                    recommendations: '',
                    observations: '',
                    photos: [],
                    history: []
                },

                init() {
                    this.loadPathology();
                },                loadPathology() {
                    this.isLoading = true;
                    const urlParams = new URLSearchParams(window.location.search);
                    const pathologyId = urlParams.get('id');
                    this.systemId = urlParams.get('system');
                    this.environmentId = urlParams.get('environment');
                    this.floorId = urlParams.get('floor');
                    this.buildingId = urlParams.get('building');
                    this.inspectionId = urlParams.get('inspection');
                    
                    // Carregar dados do localStorage primeiro
                    const savedPathologies = JSON.parse(localStorage.getItem('pathologies') || '{}');
                    
                    // Dados sincronizados das patologias
                    const pathologiesData = {
                        'PAT-001': {
                            id: 'PAT-001',
                            name: 'Oxidação nos Conectores',
                            type: 'Elétrica',
                            severity: 'Baixa',
                            status: 'Identificada',
                            identification_date: '2024-03-10',
                            correction_deadline: '2024-04-10',
                            location: {
                                x: 2.5,
                                y: 1.2,
                                height: 1.8,
                                reference: 'Quadro de distribuição principal - conectores superiores'
                            },
                            description: 'Presença de oxidação leve nos conectores do quadro de distribuição principal. A oxidação está em estágio inicial e não compromete imediatamente o funcionamento do sistema.',
                            possible_causes: 'Possíveis causas: umidade natural do ambiente, idade dos componentes (4 anos), processo natural de oxidação dos materiais.',
                            recommendations: 'Recomenda-se: limpeza dos conectores, aplicação de produto anticorrosivo, estabelecimento de rotina de inspeção trimestral.',
                            observations: 'Patologia de baixa prioridade. Sistema funcionando normalmente. Manutenção preventiva recomendada.',
                            photos: [
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Vista geral dos conectores com oxidação leve',
                                    photo_type: 'Geral'
                                },
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Detalhe da oxidação superficial',
                                    photo_type: 'Detalhe'
                                }
                            ],
                            history: [
                                {
                                    id: 1,
                                    date: '2024-03-10 10:30',
                                    user: 'João Kleber - Inspetor',
                                    description: 'Patologia identificada durante inspeção INSP-001',
                                    status: 'Identificada',
                                    details: 'Primeira identificação durante inspeção do Edifício Central'
                                },
                                {
                                    id: 2,
                                    date: '2024-03-10 14:15',
                                    user: 'Maria Fernanda - Inspetora',
                                    description: 'Documentação fotográfica realizada',
                                    status: 'Identificada',
                                    details: 'Registro fotográfico e medições realizadas'
                                },
                                {
                                    id: 3,
                                    date: '2024-03-12 09:00',
                                    user: 'Dr. Carlos Silva - Coordenador',
                                    description: 'Classificação como baixa prioridade',
                                    status: 'Identificada',
                                    details: 'Análise técnica completa e classificação de prioridade'
                                }
                            ]
                        },
                        'PAT-002': {
                            id: 'PAT-002',
                            name: 'Desgaste nos Disjuntores',
                            type: 'Elétrica',
                            severity: 'Baixa',
                            status: 'Em Análise',
                            identification_date: '2024-03-12',
                            correction_deadline: '2024-06-12',
                            location: {
                                x: 2.3,
                                y: 1.5,
                                height: 1.6,
                                reference: 'Quadro de distribuição principal - disjuntores centrais'
                            },
                            description: 'Disjuntores apresentando sinais normais de desgaste devido ao uso regular. Funcionamento dentro dos parâmetros esperados.',
                            possible_causes: 'Possíveis causas: uso normal dos equipamentos, tempo de operação (4 anos), ciclos normais de acionamento.',
                            recommendations: 'Recomenda-se: monitoramento contínuo mensal, planejamento de substituição preventiva em 2-3 anos.',
                            observations: 'Desgaste normal esperado. Sistema funcionando adequadamente. Não há urgência para substituição.',
                            photos: [
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Estado atual dos disjuntores',
                                    photo_type: 'Geral'
                                }
                            ],
                            history: [
                                {
                                    id: 1,
                                    date: '2024-03-12 11:00',
                                    user: 'João Kleber - Inspetor',
                                    description: 'Identificado durante inspeção INSP-001',
                                    status: 'Identificada',
                                    details: 'Observação durante inspeção sistemática'
                                },
                                {
                                    id: 2,
                                    date: '2024-03-15 09:30',
                                    user: 'Dr. Carlos Silva - Coordenador',
                                    description: 'Análise técnica em andamento',
                                    status: 'Em Análise',
                                    details: 'Avaliação técnica do estado dos componentes'
                                }
                            ]
                        },
                        'PAT-003': {
                            id: 'PAT-003',
                            name: 'Vazamento na Tubulação',
                            type: 'Hidráulica',
                            severity: 'Média',
                            status: 'Em Reparo',
                            identification_date: '2024-02-15',
                            correction_deadline: '2024-03-15',
                            location: {
                                x: 4.2,
                                y: 2.8,
                                height: 0.5,
                                reference: 'Tubulação de água fria - próximo à pia principal'
                            },
                            description: 'Pequeno vazamento identificado na tubulação de água fria da área de alimentação. Volume controlado, mas necessita reparo.',
                            possible_causes: 'Possíveis causas: desgaste natural da tubulação, pressão excessiva no sistema, qualidade da água (corrosividade).',
                            recommendations: 'Recomenda-se: substituição do trecho afetado, ajuste de pressão do sistema, inspeção preventiva das tubulações adjacentes.',
                            observations: 'Vazamento sob controle. Reparo em andamento. Previsão de conclusão em 5 dias.',
                            photos: [
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Local do vazamento',
                                    photo_type: 'Geral'
                                },
                                {
                                    url: '../ícones/disruption.png',
                                    description: 'Extensão do dano',
                                    photo_type: 'Detalhe'
                                }
                            ],
                            history: [
                                {
                                    id: 1,
                                    date: '2024-02-15 14:20',
                                    user: 'Carlos Mendes - Inspetor',
                                    description: 'Vazamento identificado durante inspeção INSP-002',
                                    status: 'Identificada',
                                    details: 'Descoberta durante inspeção do Shopping Plaza'
                                },
                                {
                                    id: 2,
                                    date: '2024-02-20 10:00',
                                    user: 'Eng. Maria Santos - Coordenadora',
                                    description: 'Orçamento aprovado para reparo',
                                    status: 'Em Análise',
                                    details: 'Aprovação de recursos para correção'
                                },
                                {
                                    id: 3,
                                    date: '2024-03-01 08:00',
                                    user: 'Equipe de Manutenção',
                                    description: 'Reparo iniciado',
                                    status: 'Em Reparo',
                                    details: 'Início dos trabalhos de correção'
                                }
                            ]
                        }
                    };

                    if (pathologyId) {
                        // Priorizar dados salvos no localStorage
                        if (savedPathologies[pathologyId]) {
                            this.pathology = { ...savedPathologies[pathologyId] };
                        } else if (pathologiesData[pathologyId]) {
                            this.pathology = { ...pathologiesData[pathologyId] };
                        } else {
                            this.pathology = { ...pathologiesData['PAT-001'] };
                        }
                    } else {
                        this.pathology = { ...pathologiesData['PAT-001'] };
                    }
                    
                    this.isLoading = false;
                },

                validateForm() {
                    const required = ['name', 'type', 'severity', 'status', 'identification_date', 'description'];
                    const missing = required.filter(field => !this.pathology[field] || this.pathology[field].trim() === '');
                    
                    if (missing.length > 0) {
                        alert(`Por favor, preencha os campos obrigatórios: ${missing.join(', ')}`);
                        return false;
                    }
                    
                    // Validar data de identificação
                    const identificationDate = new Date(this.pathology.identification_date);
                    const today = new Date();
                    if (identificationDate > today) {
                        alert('A data de identificação não pode ser no futuro.');
                        return false;
                    }
                    
                    // Validar prazo de correção
                    if (this.pathology.correction_deadline) {
                        const correctionDate = new Date(this.pathology.correction_deadline);
                        if (correctionDate < identificationDate) {
                            alert('O prazo de correção deve ser posterior à data de identificação.');
                            return false;
                        }
                    }
                    
                    return true;
                },

                toggleEditMode() {
                    if (this.editMode) {
                        if (this.validateForm()) {
                            this.savePathology();
                        } else {
                            return; // Não sair do modo de edição se houver erros
                        }
                    }
                    this.editMode = !this.editMode;
                },

                savePathology() {
                    this.isLoading = true;
                    
                    // Adicionar entrada no histórico
                    const newHistoryEntry = {
                        id: Date.now(),
                        date: new Date().toLocaleString('pt-BR'),
                        user: 'Usuário Atual - Inspetor',
                        description: 'Dados da patologia atualizados',
                        status: this.pathology.status,
                        details: 'Atualização realizada através da interface do inspetor'
                    };
                    this.pathology.history.unshift(newHistoryEntry);
                    
                    // Salvar no localStorage
                    const savedPathologies = JSON.parse(localStorage.getItem('pathologies') || '{}');
                    savedPathologies[this.pathology.id] = { ...this.pathology };
                    localStorage.setItem('pathologies', JSON.stringify(savedPathologies));
                    
                    // Simular delay de salvamento
                    setTimeout(() => {
                        this.isLoading = false;
                        this.showSuccessMessage('Patologia salva com sucesso!');
                    }, 500);
                },

                showSuccessMessage(message) {
                    // Criar e mostrar notificação de sucesso
                    const notification = document.createElement('div');
                    notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300';
                    notification.textContent = message;
                    document.body.appendChild(notification);
                    
                    setTimeout(() => {
                        notification.style.opacity = '0';
                        setTimeout(() => document.body.removeChild(notification), 300);
                    }, 3000);
                },                getStatusColor(status) {
                    const colors = {
                        'Identificada': 'bg-[#e6f7ff] text-[#007bff]',
                        'Em Análise': 'bg-[#f9f0ff] text-[#722ed1]',
                        'Em Correção': 'bg-[#fffbe6] text-[#faad14]',
                        'Em Reparo': 'bg-[#fffbe6] text-[#faad14]',
                        'Corrigida': 'bg-[#f6ffed] text-[#52c41a]',
                        'Monitoramento': 'bg-[#f9f0ff] text-[#722ed1]'
                    };
                    return colors[status] || 'bg-[#f5f5f5] text-[#8c8c8c]';
                },

                getSeverityColor(severity) {
                    const colors = {
                        'Baixa': 'bg-green-100 text-green-800',
                        'Média': 'bg-yellow-100 text-yellow-800',
                        'Alta': 'bg-red-100 text-red-800',
                        'Crítica': 'bg-red-200 text-red-900'
                    };
                    return colors[severity] || 'bg-gray-100 text-gray-800';
                },

                addPhoto() {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'image/*';
                    input.multiple = true;
                    input.onchange = (e) => {
                        const files = Array.from(e.target.files);
                        files.forEach(file => {
                            if (file && file.type.startsWith('image/')) {
                                const url = URL.createObjectURL(file);
                                this.pathology.photos.push({
                                    url: url,
                                    description: `Nova foto - ${new Date().toLocaleString('pt-BR')}`,
                                    photo_type: 'Geral',
                                    filename: file.name,
                                    size: file.size
                                });
                            }
                        });
                    };
                    input.click();
                },

                removePhoto(index) {
                    if (confirm('Tem certeza que deseja remover esta foto?')) {
                        // Revogar URL se for um object URL
                        const photo = this.pathology.photos[index];
                        if (photo.url.startsWith('blob:')) {
                            URL.revokeObjectURL(photo.url);
                        }
                        this.pathology.photos.splice(index, 1);
                    }
                },

                viewPhoto(photo) {
                    this.currentPhoto = { ...photo };
                    this.showPhotoModal = true;
                },

                closePhotoModal() {
                    this.showPhotoModal = false;
                    this.currentPhoto = null;
                },

                savePhotoDescription() {
                    const index = this.pathology.photos.findIndex(p => p.url === this.currentPhoto.url);
                    if (index !== -1) {
                        this.pathology.photos[index].description = this.currentPhoto.description;
                        this.pathology.photos[index].photo_type = this.currentPhoto.photo_type;
                    }
                    this.closePhotoModal();
                }
            }
        }

        function goBack() {
            const urlParams = new URLSearchParams(window.location.search);
            const systemId = urlParams.get('system');
            const environmentId = urlParams.get('environment');
            const floorId = urlParams.get('floor');
            const buildingId = urlParams.get('building');
            const inspectionId = urlParams.get('inspection');
            
            if (systemId && environmentId && floorId && buildingId && inspectionId) {
                window.location.href = `detalhesSistema.html?id=${systemId}&environment=${environmentId}&floor=${floorId}&building=${buildingId}&inspection=${inspectionId}`;
            } else {
                window.location.href = '/listaInspecoes';
            }
        }
    </script>
</body>

</html>
