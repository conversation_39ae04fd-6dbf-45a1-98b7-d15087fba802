const express = require('express');
const router = express.Router();
const path = require('path');

// Middleware para servir arquivos estáticos (imagens, CSS, JS)
router.use('/images', express.static(path.join(__dirname, '../views/pages/ícones')));
router.use('/css', express.static(path.join(__dirname, '../views/css')));
router.use('/js', express.static(path.join(__dirname, '../views/js')));

// ==================== ROTAS DE LOGIN ====================
router.get('/', (req, res) => {
  res.render('pages/login/login', {
    pageTitle: 'InfraWatch - Login'
  });
});

router.get('/login', (req, res) => {
  res.render('pages/login/login', {
    pageTitle: 'InfraWatch - Login'
  });
});

router.get('/cadastro', (req, res) => {
  res.render('pages/login/cadastro', {
    pageTitle: 'InfraWatch - Cadastro'
  });
});

router.get('/recuperar-senha', (req, res) => {
  res.render('pages/login/recuperarSenha', {
    pageTitle: 'InfraWatch - Recuperar Senha'
  });
});

// ==================== ROTAS ADMIN ====================
router.get('/admin/dashboard', (req, res) => {
  res.render('pages/admin/dashboard', {
    pageTitle: 'Painel Administrativo',
    userRole: 'admin'
  });
});

router.get('/admin/membros', (req, res) => {
  res.render('pages/admin/membros', {
    pageTitle: 'Gerenciar Membros',
    userRole: 'admin'
  });
});

router.get('/admin/relatorios', (req, res) => {
  res.render('pages/admin/relatorios', {
    pageTitle: 'Relatórios',
    userRole: 'admin'
  });
});

router.get('/admin/nova-inspecao', (req, res) => {
  res.render('pages/admin/novaInspecao', {
    pageTitle: 'Nova Inspeção',
    userRole: 'admin'
  });
});

router.get('/admin/detalhes-inspecao', (req, res) => {
  const inspecaoId = req.query.id;
  res.render('pages/admin/detalhesInspecao', {
    pageTitle: 'Detalhes da Inspeção',
    userRole: 'admin',
    inspecaoId: inspecaoId
  });
});

router.get('/admin/editar-inspecao', (req, res) => {
  const inspecaoId = req.query.id;
  res.render('pages/admin/editarInspecao', {
    pageTitle: 'Editar Inspeção',
    userRole: 'admin',
    inspecaoId: inspecaoId
  });
});

router.get('/admin/visualizar-relatorio', (req, res) => {
  const relatorioId = req.query.id;
  res.render('pages/admin/visualizarRelatorio', {
    pageTitle: 'Visualizar Relatório',
    userRole: 'admin',
    relatorioId: relatorioId
  });
});

router.get('/admin/detalhes-ambiente', (req, res) => {
  const ambienteId = req.query.id;
  res.render('pages/admin/detalhesAmbiente', {
    pageTitle: 'Detalhes do Ambiente',
    userRole: 'admin',
    ambienteId: ambienteId
  });
});

router.get('/admin/detalhes-edificio', (req, res) => {
  const edificioId = req.query.id;
  res.render('pages/admin/detalhesEdificio', {
    pageTitle: 'Detalhes do Edifício',
    userRole: 'admin',
    edificioId: edificioId
  });
});

router.get('/admin/detalhes-membro', (req, res) => {
  const membroId = req.query.id;
  res.render('pages/admin/detalhesMembro', {
    pageTitle: 'Detalhes do Membro',
    userRole: 'admin',
    membroId: membroId
  });
});

router.get('/admin/detalhes-patologia', (req, res) => {
  const patologiaId = req.query.id;
  res.render('pages/admin/detalhesPatologia', {
    pageTitle: 'Detalhes da Patologia',
    userRole: 'admin',
    patologiaId: patologiaId
  });
});

router.get('/admin/detalhes-pavimento', (req, res) => {
  const pavimentoId = req.query.id;
  res.render('pages/admin/detalhesPavimento', {
    pageTitle: 'Detalhes do Pavimento',
    userRole: 'admin',
    pavimentoId: pavimentoId
  });
});

router.get('/admin/detalhes-sistema', (req, res) => {
  const sistemaId = req.query.id;
  res.render('pages/admin/detalhesSistema', {
    pageTitle: 'Detalhes do Sistema',
    userRole: 'admin',
    sistemaId: sistemaId
  });
});

// ==================== ROTAS INSPETOR ====================
router.get('/inspetor/lista-inspecoes', (req, res) => {
  res.render('pages/inspetor/listaInspecoes', {
    pageTitle: 'Lista de Inspeções',
    userRole: 'inspetor'
  });
});

router.get('/inspetor/iniciar-inspecao', (req, res) => {
  const inspecaoId = req.query.id;
  res.render('pages/inspetor/iniciarInspecao', {
    pageTitle: 'Iniciar Inspeção',
    userRole: 'inspetor',
    inspecaoId: inspecaoId
  });
});

router.get('/inspetor/perfil', (req, res) => {
  res.render('pages/inspetor/perfil', {
    pageTitle: 'Meu Perfil',
    userRole: 'inspetor'
  });
});

router.get('/inspetor/relatorios', (req, res) => {
  res.render('pages/inspetor/relatorios', {
    pageTitle: 'Meus Relatórios',
    userRole: 'inspetor'
  });
});

router.get('/inspetor/detalhes-inspecao', (req, res) => {
  const inspecaoId = req.query.id;
  res.render('pages/inspetor/detalhesInspecao', {
    pageTitle: 'Detalhes da Inspeção',
    userRole: 'inspetor',
    inspecaoId: inspecaoId
  });
});

router.get('/inspetor/visualizar-relatorio', (req, res) => {
  const relatorioId = req.query.id;
  res.render('pages/inspetor/visualizarRelatorio', {
    pageTitle: 'Visualizar Relatório',
    userRole: 'inspetor',
    relatorioId: relatorioId
  });
});

router.get('/inspetor/detalhes-ambiente', (req, res) => {
  const ambienteId = req.query.id;
  res.render('pages/inspetor/detalhesAmbiente', {
    pageTitle: 'Detalhes do Ambiente',
    userRole: 'inspetor',
    ambienteId: ambienteId
  });
});

router.get('/inspetor/detalhes-edificio', (req, res) => {
  const edificioId = req.query.id;
  res.render('pages/inspetor/detalhesEdificio', {
    pageTitle: 'Detalhes do Edifício',
    userRole: 'inspetor',
    edificioId: edificioId
  });
});

router.get('/inspetor/detalhes-patologia', (req, res) => {
  const patologiaId = req.query.id;
  res.render('pages/inspetor/detalhesPatologia', {
    pageTitle: 'Detalhes da Patologia',
    userRole: 'inspetor',
    patologiaId: patologiaId
  });
});

router.get('/inspetor/detalhes-pavimento', (req, res) => {
  const pavimentoId = req.query.id;
  res.render('pages/inspetor/detalhesPavimento', {
    pageTitle: 'Detalhes do Pavimento',
    userRole: 'inspetor',
    pavimentoId: pavimentoId
  });
});

router.get('/inspetor/detalhes-sistema', (req, res) => {
  const sistemaId = req.query.id;
  res.render('pages/inspetor/detalhesSistema', {
    pageTitle: 'Detalhes do Sistema',
    userRole: 'inspetor',
    sistemaId: sistemaId
  });
});

module.exports = router;
