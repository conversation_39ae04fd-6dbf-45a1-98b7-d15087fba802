# Guia de Conversão HTML para EJS - InfraWatch

## Resumo da Conversão

✅ **CONVERSÃO CONCLUÍDA COM SUCESSO!**

Todos os arquivos HTML do diretório `src/views/pages` foram convertidos para o formato EJS (.ejs) e integrados ao sistema Express com EJS como view engine.

## Arquivos Convertidos

### Pasta Login (3 arquivos)
- `login.html` → `login.ejs`
- `cadastro.html` → `cadastro.ejs`
- `recuperarSenha.html` → `recuperarSenha.ejs`

### Pasta Admin (13 arquivos)
- `dashboard.html` → `dashboard.ejs`
- `detalhesAmbiente.html` → `detalhesAmbiente.ejs`
- `detalhesEdificio.html` → `detalhesEdificio.ejs`
- `detalhesInspecao.html` → `detalhesInspecao.ejs`
- `detalhesMembro.html` → `detalhesMembro.ejs`
- `detalhesPatologia.html` → `detalhesPatologia.ejs`
- `detalhesPavimento.html` → `detalhesPavimento.ejs`
- `detalhesSistema.html` → `detalhesSistema.ejs`
- `editarInspecao.html` → `editarInspecao.ejs`
- `membros.html` → `membros.ejs`
- `novaInspecao.html` → `novaInspecao.ejs`
- `relatorios.html` → `relatorios.ejs`
- `visualizarRelatorio.html` → `visualizarRelatorio.ejs`

### Pasta Inspetor (11 arquivos)
- `detalhesAmbiente.html` → `detalhesAmbiente.ejs`
- `detalhesEdificio.html` → `detalhesEdificio.ejs`
- `detalhesInspecao.html` → `detalhesInspecao.ejs`
- `detalhesPatologia.html` → `detalhesPatologia.ejs`
- `detalhesPavimento.html` → `detalhesPavimento.ejs`
- `detalhesSistema.html` → `detalhesSistema.ejs`
- `iniciarInspecao.html` → `iniciarInspecao.ejs`
- `listaInspecoes.html` → `listaInspecoes.ejs`
- `perfil.html` → `perfil.ejs`
- `relatorios.html` → `relatorios.ejs`
- `visualizarRelatorio.html` → `visualizarRelatorio.ejs`

**Total: 27 arquivos convertidos**

## Mudanças Estruturais Implementadas

### 1. Configuração do Express
- ✅ EJS já estava configurado no `server.js`
- ✅ View engine definido como 'ejs'
- ✅ Diretório views configurado corretamente

### 2. Rotas Atualizadas (`frontRoutes.js`)
Todas as rotas foram criadas e organizadas por categoria:

#### Rotas de Login
- `GET /` → Renderiza `pages/login/login`
- `GET /login` → Renderiza `pages/login/login`
- `GET /cadastro` → Renderiza `pages/login/cadastro`
- `GET /recuperar-senha` → Renderiza `pages/login/recuperarSenha`

#### Rotas Admin
- `GET /admin/dashboard` → Renderiza `pages/admin/dashboard`
- `GET /admin/membros` → Renderiza `pages/admin/membros`
- `GET /admin/relatorios` → Renderiza `pages/admin/relatorios`
- `GET /admin/nova-inspecao` → Renderiza `pages/admin/novaInspecao`
- `GET /admin/detalhes-inspecao` → Renderiza `pages/admin/detalhesInspecao`
- `GET /admin/editar-inspecao` → Renderiza `pages/admin/editarInspecao`
- `GET /admin/visualizar-relatorio` → Renderiza `pages/admin/visualizarRelatorio`
- `GET /admin/detalhes-ambiente` → Renderiza `pages/admin/detalhesAmbiente`
- `GET /admin/detalhes-edificio` → Renderiza `pages/admin/detalhesEdificio`
- `GET /admin/detalhes-membro` → Renderiza `pages/admin/detalhesMembro`
- `GET /admin/detalhes-patologia` → Renderiza `pages/admin/detalhesPatologia`
- `GET /admin/detalhes-pavimento` → Renderiza `pages/admin/detalhesPavimento`
- `GET /admin/detalhes-sistema` → Renderiza `pages/admin/detalhesSistema`

#### Rotas Inspetor
- `GET /inspetor/lista-inspecoes` → Renderiza `pages/inspetor/listaInspecoes`
- `GET /inspetor/iniciar-inspecao` → Renderiza `pages/inspetor/iniciarInspecao`
- `GET /inspetor/perfil` → Renderiza `pages/inspetor/perfil`
- `GET /inspetor/relatorios` → Renderiza `pages/inspetor/relatorios`
- `GET /inspetor/detalhes-inspecao` → Renderiza `pages/inspetor/detalhesInspecao`
- `GET /inspetor/visualizar-relatorio` → Renderiza `pages/inspetor/visualizarRelatorio`
- `GET /inspetor/detalhes-ambiente` → Renderiza `pages/inspetor/detalhesAmbiente`
- `GET /inspetor/detalhes-edificio` → Renderiza `pages/inspetor/detalhesEdificio`
- `GET /inspetor/detalhes-patologia` → Renderiza `pages/inspetor/detalhesPatologia`
- `GET /inspetor/detalhes-pavimento` → Renderiza `pages/inspetor/detalhesPavimento`
- `GET /inspetor/detalhes-sistema` → Renderiza `pages/inspetor/detalhesSistema`

### 3. Arquivos Estáticos
Configurado middleware para servir arquivos estáticos:
- `/images` → Serve ícones de `src/views/pages/ícones`
- `/css` → Serve arquivos CSS de `src/views/css`
- `/js` → Serve arquivos JavaScript de `src/views/js`

### 4. Ajustes nos Templates EJS

#### Links Atualizados
Todos os links HTML foram convertidos para rotas Express:
- `../admin/dashboard.html` → `/admin/dashboard`
- `../inspetor/listaInspecoes.html` → `/inspetor/lista-inspecoes`
- `cadastro.html` → `/cadastro`
- `recuperarSenha.html` → `/recuperar-senha`

#### Caminhos de Imagens
- `../ícones/` → `/images/icones/`

#### Variáveis EJS
Adicionadas variáveis dinâmicas nos templates:
- `<%= pageTitle %>` para títulos dinâmicos
- `<%= userRole %>` para controle de acesso
- `<%= inspecaoId %>`, `<%= relatorioId %>`, etc. para parâmetros

## Exemplo Prático de Conversão

### Página de Login - Antes e Depois

#### Antes (login.html)
```html
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="utf-8" />
    <title>InfraWatch - Login</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Work+Sans" />
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div class="login-container">
        <h2>Login</h2>
        <form id="loginForm">
            <!-- campos do formulário -->
            <a href="recuperarSenha.html">Problemas para entrar?</a>
            <a href="cadastro.html">Crie seu Cadastro</a>
        </form>
    </div>
    <script>
        if (email === '<EMAIL>') {
            window.location.href = '../admin/dashboard.html';
        } else if (email === '<EMAIL>') {
            window.location.href = '../inspetor/listaInspecoes.html';
        }
    </script>
</body>
</html>
```

#### Depois (login.ejs)
```html
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="utf-8" />
    <title><%= pageTitle %></title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Work+Sans" />
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div class="login-container">
        <h2>Login</h2>
        <form id="loginForm">
            <!-- campos do formulário -->
            <a href="/recuperar-senha">Problemas para entrar?</a>
            <a href="/cadastro">Crie seu Cadastro</a>
        </form>
    </div>
    <script>
        if (email === '<EMAIL>') {
            window.location.href = '/admin/dashboard';
        } else if (email === '<EMAIL>') {
            window.location.href = '/inspetor/lista-inspecoes';
        }
    </script>
</body>
</html>
```

### Principais Mudanças Aplicadas:
1. **Título dinâmico:** `<title><%= pageTitle %></title>`
2. **Links relativos → rotas Express:** `cadastro.html` → `/cadastro`
3. **Navegação JavaScript:** `../admin/dashboard.html` → `/admin/dashboard`
4. **Caminhos de imagens:** `../ícones/` → `/images/icones/`
5. **Estrutura mantida:** Todo o CSS e JavaScript original preservado

## Como Testar

### ✅ Teste de Inicialização Realizado
O servidor foi testado e está funcionando corretamente. O único erro encontrado foi relacionado à conexão com o banco de dados (credenciais), o que é esperado.

1. **Iniciar o servidor:**
   ```bash
   cd src
   node server.js
   # ou
   npm start
   ```

2. **Resultado do teste:**
   - ✅ Servidor inicia corretamente
   - ✅ EJS está configurado e funcionando
   - ✅ Rotas estão carregando
   - ⚠️ Erro de conexão com banco (esperado - credenciais não configuradas)

3. **Acessar as rotas (após configurar banco):**
   - Login: `http://localhost:3000/`
   - Admin Dashboard: `http://localhost:3000/admin/dashboard`
   - Lista Inspeções: `http://localhost:3000/inspetor/lista-inspecoes`

4. **Testar navegação:**
   - Todos os links internos devem funcionar
   - Imagens devem carregar corretamente
   - JavaScript deve funcionar normalmente

### Configuração do Banco de Dados
Para testar completamente, configure as credenciais do banco no arquivo `.env`:
```env
DB_HOST=seu_host
DB_PORT=5432
DB_NAME=seu_banco
DB_USER=seu_usuario
DB_PASSWORD=sua_senha
```

## Benefícios da Conversão

1. **Integração Completa:** Todas as páginas agora fazem parte do sistema Express
2. **Templates Dinâmicos:** Possibilidade de passar dados do servidor para as views
3. **Roteamento Consistente:** URLs limpos e organizados
4. **Manutenibilidade:** Estrutura mais organizada e fácil de manter
5. **Escalabilidade:** Base sólida para futuras funcionalidades

## Próximos Passos Recomendados

1. **Implementar Autenticação:** Adicionar middleware de autenticação nas rotas
2. **Conectar com APIs:** Integrar as páginas com os controllers existentes
3. **Adicionar Layouts:** Criar layouts compartilhados para reduzir duplicação
4. **Implementar Partials:** Extrair componentes reutilizáveis
5. **Testes:** Criar testes para as rotas e templates

## Arquivos Criados/Modificados

- ✅ `src/routes/frontRoutes.js` - Rotas atualizadas
- ✅ `src/scripts/convertHtmlToEjs.js` - Script de conversão
- ✅ `src/views/layout/standalone.ejs` - Layout para páginas independentes
- ✅ Todos os arquivos `.ejs` nas pastas `pages/`

A conversão foi realizada com sucesso e o sistema está pronto para uso!
