# Guia de Conversão HTML para EJS - InfraWatch

## Resumo da Conversão

✅ **CONVERSÃO CONCLUÍDA COM SUCESSO!**

Todos os arquivos HTML do diretório `src/views/pages` foram convertidos para o formato EJS (.ejs) e integrados ao sistema Express com EJS como view engine.

## Arquivos Convertidos

### Pasta Login (3 arquivos)
- `login.html` → `login.ejs`
- `cadastro.html` → `cadastro.ejs`
- `recuperarSenha.html` → `recuperarSenha.ejs`

### Pasta Admin (13 arquivos)
- `dashboard.html` → `dashboard.ejs`
- `detalhesAmbiente.html` → `detalhesAmbiente.ejs`
- `detalhesEdificio.html` → `detalhesEdificio.ejs`
- `detalhesInspecao.html` → `detalhesInspecao.ejs`
- `detalhesMembro.html` → `detalhesMembro.ejs`
- `detalhesPatologia.html` → `detalhesPatologia.ejs`
- `detalhesPavimento.html` → `detalhesPavimento.ejs`
- `detalhesSistema.html` → `detalhesSistema.ejs`
- `editarInspecao.html` → `editarInspecao.ejs`
- `membros.html` → `membros.ejs`
- `novaInspecao.html` → `novaInspecao.ejs`
- `relatorios.html` → `relatorios.ejs`
- `visualizarRelatorio.html` → `visualizarRelatorio.ejs`

### Pasta Inspetor (11 arquivos)
- `detalhesAmbiente.html` → `detalhesAmbiente.ejs`
- `detalhesEdificio.html` → `detalhesEdificio.ejs`
- `detalhesInspecao.html` → `detalhesInspecao.ejs`
- `detalhesPatologia.html` → `detalhesPatologia.ejs`
- `detalhesPavimento.html` → `detalhesPavimento.ejs`
- `detalhesSistema.html` → `detalhesSistema.ejs`
- `iniciarInspecao.html` → `iniciarInspecao.ejs`
- `listaInspecoes.html` → `listaInspecoes.ejs`
- `perfil.html` → `perfil.ejs`
- `relatorios.html` → `relatorios.ejs`
- `visualizarRelatorio.html` → `visualizarRelatorio.ejs`

**Total: 27 arquivos convertidos**

## Mudanças Estruturais Implementadas

### 1. Configuração do Express
- ✅ EJS já estava configurado no `server.js`
- ✅ View engine definido como 'ejs'
- ✅ Diretório views configurado corretamente

### 2. Rotas Atualizadas (`frontRoutes.js`)
Todas as rotas foram criadas e organizadas por categoria:

#### Rotas de Login
- `GET /` → Renderiza `pages/login/login`
- `GET /login` → Renderiza `pages/login/login`
- `GET /cadastro` → Renderiza `pages/login/cadastro`
- `GET /recuperar-senha` → Renderiza `pages/login/recuperarSenha`

#### Rotas Admin
- `GET /admin/dashboard` → Renderiza `pages/admin/dashboard`
- `GET /admin/membros` → Renderiza `pages/admin/membros`
- `GET /admin/relatorios` → Renderiza `pages/admin/relatorios`
- `GET /admin/nova-inspecao` → Renderiza `pages/admin/novaInspecao`
- `GET /admin/detalhes-inspecao` → Renderiza `pages/admin/detalhesInspecao`
- `GET /admin/editar-inspecao` → Renderiza `pages/admin/editarInspecao`
- `GET /admin/visualizar-relatorio` → Renderiza `pages/admin/visualizarRelatorio`
- `GET /admin/detalhes-ambiente` → Renderiza `pages/admin/detalhesAmbiente`
- `GET /admin/detalhes-edificio` → Renderiza `pages/admin/detalhesEdificio`
- `GET /admin/detalhes-membro` → Renderiza `pages/admin/detalhesMembro`
- `GET /admin/detalhes-patologia` → Renderiza `pages/admin/detalhesPatologia`
- `GET /admin/detalhes-pavimento` → Renderiza `pages/admin/detalhesPavimento`
- `GET /admin/detalhes-sistema` → Renderiza `pages/admin/detalhesSistema`

#### Rotas Inspetor
- `GET /inspetor/lista-inspecoes` → Renderiza `pages/inspetor/listaInspecoes`
- `GET /inspetor/iniciar-inspecao` → Renderiza `pages/inspetor/iniciarInspecao`
- `GET /inspetor/perfil` → Renderiza `pages/inspetor/perfil`
- `GET /inspetor/relatorios` → Renderiza `pages/inspetor/relatorios`
- `GET /inspetor/detalhes-inspecao` → Renderiza `pages/inspetor/detalhesInspecao`
- `GET /inspetor/visualizar-relatorio` → Renderiza `pages/inspetor/visualizarRelatorio`
- `GET /inspetor/detalhes-ambiente` → Renderiza `pages/inspetor/detalhesAmbiente`
- `GET /inspetor/detalhes-edificio` → Renderiza `pages/inspetor/detalhesEdificio`
- `GET /inspetor/detalhes-patologia` → Renderiza `pages/inspetor/detalhesPatologia`
- `GET /inspetor/detalhes-pavimento` → Renderiza `pages/inspetor/detalhesPavimento`
- `GET /inspetor/detalhes-sistema` → Renderiza `pages/inspetor/detalhesSistema`

### 3. Arquivos Estáticos
Configurado middleware para servir arquivos estáticos:
- `/images` → Serve ícones de `src/views/pages/ícones`
- `/css` → Serve arquivos CSS de `src/views/css`
- `/js` → Serve arquivos JavaScript de `src/views/js`

### 4. Ajustes nos Templates EJS

#### Links Atualizados
Todos os links HTML foram convertidos para rotas Express:
- `../admin/dashboard.html` → `/admin/dashboard`
- `../inspetor/listaInspecoes.html` → `/inspetor/lista-inspecoes`
- `cadastro.html` → `/cadastro`
- `recuperarSenha.html` → `/recuperar-senha`

#### Caminhos de Imagens
- `../ícones/` → `/images/icones/`

#### Variáveis EJS
Adicionadas variáveis dinâmicas nos templates:
- `<%= pageTitle %>` para títulos dinâmicos
- `<%= userRole %>` para controle de acesso
- `<%= inspecaoId %>`, `<%= relatorioId %>`, etc. para parâmetros

## Exemplo de Conversão

### Antes (HTML)
```html
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <title>InfraWatch - Login</title>
    <!-- ... -->
</head>
<body>
    <!-- conteúdo -->
    <a href="cadastro.html">Cadastro</a>
    <script>
        window.location.href = '../admin/dashboard.html';
    </script>
</body>
</html>
```

### Depois (EJS)
```html
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <title><%= pageTitle %></title>
    <!-- ... -->
</head>
<body>
    <!-- conteúdo -->
    <a href="/cadastro">Cadastro</a>
    <script>
        window.location.href = '/admin/dashboard';
    </script>
</body>
</html>
```

## Como Testar

1. **Iniciar o servidor:**
   ```bash
   cd src
   npm start
   ```

2. **Acessar as rotas:**
   - Login: `http://localhost:3000/`
   - Admin Dashboard: `http://localhost:3000/admin/dashboard`
   - Lista Inspeções: `http://localhost:3000/inspetor/lista-inspecoes`

3. **Testar navegação:**
   - Todos os links internos devem funcionar
   - Imagens devem carregar corretamente
   - JavaScript deve funcionar normalmente

## Benefícios da Conversão

1. **Integração Completa:** Todas as páginas agora fazem parte do sistema Express
2. **Templates Dinâmicos:** Possibilidade de passar dados do servidor para as views
3. **Roteamento Consistente:** URLs limpos e organizados
4. **Manutenibilidade:** Estrutura mais organizada e fácil de manter
5. **Escalabilidade:** Base sólida para futuras funcionalidades

## Próximos Passos Recomendados

1. **Implementar Autenticação:** Adicionar middleware de autenticação nas rotas
2. **Conectar com APIs:** Integrar as páginas com os controllers existentes
3. **Adicionar Layouts:** Criar layouts compartilhados para reduzir duplicação
4. **Implementar Partials:** Extrair componentes reutilizáveis
5. **Testes:** Criar testes para as rotas e templates

## Arquivos Criados/Modificados

- ✅ `src/routes/frontRoutes.js` - Rotas atualizadas
- ✅ `src/scripts/convertHtmlToEjs.js` - Script de conversão
- ✅ `src/views/layout/standalone.ejs` - Layout para páginas independentes
- ✅ Todos os arquivos `.ejs` nas pastas `pages/`

A conversão foi realizada com sucesso e o sistema está pronto para uso!
