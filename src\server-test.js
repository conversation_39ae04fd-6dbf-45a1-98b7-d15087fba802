require('dotenv').config();
const express = require('express');
const app = express();
const path = require('path');

// Configuração do EJS
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Middleware para parsing JSON
app.use(express.json());

// Middleware para arquivos estáticos
app.use('/images', express.static(path.join(__dirname, 'views/pages/ícones')));
app.use('/css', express.static(path.join(__dirname, 'views/css')));
app.use('/js', express.static(path.join(__dirname, 'views/js')));

// Importar rotas do frontend
const frontendRoutes = require('./routes/frontRoutes');
app.use('/', frontendRoutes);

// Middleware para lidar com erros de rota não encontrada
app.use((req, res, next) => {
  res.status(404).send(`
    <h1>Página não encontrada</h1>
    <p>A rota <strong>${req.path}</strong> não foi encontrada.</p>
    <p><a href="/">Voltar ao início</a></p>
  `);
});

// Middleware para lidar com erros internos do servidor
app.use((err, req, res, next) => {
  console.error('Erro no servidor:', err.stack);
  res.status(500).send(`
    <h1>Erro no servidor</h1>
    <p>Ocorreu um erro interno: ${err.message}</p>
    <p><a href="/">Voltar ao início</a></p>
  `);
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`📱 Acesse: http://localhost:${PORT}`);
  console.log(`🔗 Rotas disponíveis:`);
  console.log(`   Login: http://localhost:${PORT}/`);
  console.log(`   Admin Dashboard: http://localhost:${PORT}/admin/dashboard`);
  console.log(`   Lista Inspeções: http://localhost:${PORT}/inspetor/lista-inspecoes`);
  console.log(`\n✅ Templates EJS prontos para teste!`);
});
