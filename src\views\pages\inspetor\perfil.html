<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meu Perfil - InfraWatch</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/disruption.png" />
    <style>
        [x-cloak] {
            display: none !important;
        }

        .nav-item-active {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
            background-color: #eff6ff;
        }
    </style>
</head>

<body x-data="{
    isEditing: false,
    user: {
        name: 'Rafael Inspetor',
        email: '<EMAIL>',
        specialization: 'Engenheiro Civil',
        phone: '(11) 98765-4321',
        avatar: '../ícones/perfil.png'
    },
    tempUser: {},
    startEditing() {
        this.tempUser = JSON.parse(JSON.stringify(this.user));
        this.isEditing = true;
    },
    saveChanges() {
        this.user = JSON.parse(JSON.stringify(this.tempUser));
        this.isEditing = false;
        alert('Perfil atualizado com sucesso!');
    },
    cancelEditing() {
        this.isEditing = false;
    },
    handleAvatarChange(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.tempUser.avatar = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    }
}">
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="listaInspecoes.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="listaInspecoes.html">Inspeções</a>
                        <a class="text-[#111518] text-sm font-medium leading-normal" href="relatorios.html">Relatórios</a>
                    </div>
                </nav>

                <div class="flex justify-end gap-8 items-center">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Nova inspeção atribuída.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Prazo de inspeção se aproximando.
                                </a>
                            </div>
                        </div>
                    </div>
                    <a href="perfil.html">
                        <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 nav-item-active"
                            :style="`background-image: url('${user.avatar}');`">
                        </div>
                    </a>
                </div>
            </header>

            <div class="px-10 md:px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
                    <div class="flex flex-wrap justify-between gap-3 p-4 items-center">
                        <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight min-w-72">
                            Meu Perfil</p>                        <button x-show="!isEditing" @click="startEditing()"
                            class="flex items-center justify-center gap-2 rounded-xl bg-[#1980e6] px-4 py-2 text-white text-sm font-medium leading-normal tr-duration-300 hover:bg-[#1565c0] focus:bg-[#1565c0] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1980e6]">
                            <img src="../ícones/settings.png" alt="Editar Perfil" width="20px" height="20px" style="filter: brightness(0) invert(1);">
                            Editar Perfil
                        </button>
                    </div>

                    <div class="p-4 space-y-6">
                        <div class="flex flex-col items-center space-y-4">
                            <div class="relative">
                                <img :src="isEditing ? tempUser.avatar : user.avatar" alt="Foto do Perfil" class="w-32 h-32 rounded-full object-cover border-4 border-gray-200">
                                <label x-show="isEditing" for="avatarUpload" class="absolute bottom-0 right-0 bg-blue-500 text-white rounded-full p-2 cursor-pointer hover:bg-blue-600">
                                    <input type="file" id="avatarUpload" @change="handleAvatarChange" class="hidden" accept="image/*">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-camera-fill" viewBox="0 0 16 16">
                                        <path d="M10.5 8.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/>
                                        <path d="M2 4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2h-1.172a2 2 0 0 1-1.414-.586l-.828-.828A2 2 0 0 0 9.172 2H6.828a2 2 0 0 0-1.414.586l-.828.828A2 2 0 0 1 3.172 4H2zm.5 2a.5.5 0 1 1 0-1 .5.5 0 0 1 0 1zm9 2.5a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0z"/>
                                    </svg>
                                </label>
                            </div>
                            <h2 class="text-2xl font-semibold" x-text="user.name"></h2>
                            <p class="text-gray-600" x-text="user.specialization"></p>
                        </div>

                        <div class="border-t border-gray-200 pt-6">
                            <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-8">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Nome Completo</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        <span x-show="!isEditing" x-text="user.name"></span>
                                        <input x-show="isEditing" type="text" x-model="tempUser.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-[#f0f2f4] p-2">
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Endereço de Email</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        <span x-show="!isEditing" x-text="user.email"></span>
                                        <input x-show="isEditing" type="email" x-model="tempUser.email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-[#f0f2f4] p-2">
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Telefone</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        <span x-show="!isEditing" x-text="user.phone"></span>
                                        <input x-show="isEditing" type="tel" x-model="tempUser.phone" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-[#f0f2f4] p-2">
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Especialização</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        <span x-show="!isEditing" x-text="user.specialization"></span>
                                        <input x-show="isEditing" type="text" x-model="tempUser.specialization" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-[#f0f2f4] p-2">
                                    </dd>
                                </div>
                            </dl>
                        </div>

                        <div x-show="isEditing" class="flex justify-end space-x-3 pt-6">
                            <button @click="cancelEditing()"
                                class="rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                Cancelar
                            </button>
                            <button @click="saveChanges()"
                                class="rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                Salvar Alterações
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
