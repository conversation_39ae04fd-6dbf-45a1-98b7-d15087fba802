<html lang="pt-BR">

<head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />

    <title>Painel</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        .nav-item-active {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
            background-color: #eff6ff;
        }

        [x-cloak] {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="dashboard.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>

                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="dashboard.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center nav-item-active">
                            <img src="../ícones/botao-de-inicio.png" alt="Painel" width="20px" height="20px">
                            Painel
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                        <a href="membros.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/grupo.png" alt="Membros" width="20px" height="20px">
                            Membros
                        </a>
                    </div>
                </nav>

                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Inspeção INSP-001 atribuída à equipe de inspetores.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Relatório da inspeção INSP-003 concluído.
                                </a>
                                <div x-show="!hasNotifications" class="px-4 py-3 text-sm text-gray-500">
                                    Nenhuma nova notificação.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('../ícones/perfil.png');">
                    </div>
                </div>
            </header>

            <div class="gap-1 px-6 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col w-full max-w-none flex-1">
                    <div class="flex flex-wrap justify-between gap-3 p-4">
                        <div class="flex min-w-72 flex-col gap-3">
                            <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight">Painel</p>
                            <p class="text-[#637688] text-sm font-normal leading-normal">Visão geral de todas as
                                atividades de inspeção</p>
                        </div>
                        <a href="novaInspecao.html"
                            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#007bff] text-white text-sm font-medium leading-normal self-center">
                            <span class="truncate">Nova Inspeção</span>
                        </a>
                    </div>                    <div class="flex flex-wrap gap-4 p-4">
                        <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#f0f2f4]">
                            <p class="text-[#111518] text-base font-medium leading-normal">Total de Inspeções</p>
                            <p class="text-[#111518] tracking-light text-2xl font-bold leading-tight">3</p>
                        </div>
                        <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#f0f2f4]">
                            <p class="text-[#111518] text-base font-medium leading-normal">Inspeções em Andamento</p>
                            <p class="text-[#111518] tracking-light text-2xl font-bold leading-tight">1</p>
                        </div>
                        <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#f0f2f4]">
                            <p class="text-[#111518] text-base font-medium leading-normal">Inspeções Concluídas</p>
                            <p class="text-[#111518] tracking-light text-2xl font-bold leading-tight">1</p>
                        </div>
                        <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#f0f2f4]">
                            <p class="text-[#111518] text-base font-medium leading-normal">Próximas Inspeções</p>
                            <p class="text-[#111518] tracking-light text-2xl font-bold leading-tight">1</p>
                        </div>
                    </div>
                    <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
                        Visão Geral das Inspeções</h2>
                    <div class="px-4 py-3">
                        <label class="flex flex-col min-w-40 h-12 w-full">
                            <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                                <div class="text-[#637688] flex border-none bg-[#f0f2f4] items-center justify-center pl-4 rounded-l-xl border-r-0"
                                    data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                        fill="currentColor" viewBox="0 0 256 256">
                                        <path
                                            d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z">
                                        </path>
                                    </svg>
                                </div>
                                <input placeholder="Buscar inspeções..."
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-r-xl text-[#111518] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] h-full placeholder:text-[#637688] px-4 text-base font-normal leading-normal"
                                    value="" />
                            </div>
                        </label>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 px-4 py-3">
                        <div>
                            <label for="statusFilter" class="block text-sm font-medium text-gray-700">Filtrar por
                                Status</label>
                            <select id="statusFilter" name="statusFilter"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
                                <option value="">Todos</option>
                                <option value="Agendada">Agendada</option>
                                <option value="Em Andamento">Em Andamento</option>
                                <option value="Concluída">Concluída</option>
                            </select>
                        </div>
                        <div>
                            <label for="startDateFilter" class="block text-sm font-medium text-gray-700">Data de
                                Início</label>
                            <input type="date" id="startDateFilter" name="startDateFilter"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
                        </div>
                        <div>
                            <label for="endDateFilter" class="block text-sm font-medium text-gray-700">Data de
                                Término</label>
                            <input type="date" id="endDateFilter" name="endDateFilter"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
                        </div>
                    </div>
                    <div class="px-4 py-3 @container">
                        <div class="flex overflow-hidden rounded-xl border border-[#dce1e5] bg-white">
                            <table class="flex-1">
                                <thead>
                                    <tr class="bg-white">
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                            ID da Inspeção
                                        </th>
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[250px] text-sm font-medium leading-normal">
                                            Endereço
                                        </th>
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                            Status</th>
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                            Data de Início
                                        </th>
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                            Data de Término
                                        </th>
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[100px] text-sm font-medium leading-normal">
                                            Ações
                                        </th>
                                    </tr>
                                </thead>                                <tbody>
                                    <tr class="border-t border-t-[#dce1e5]">
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            INSP-001</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            Av. Paulista, 1500 - Bela Vista, São Paulo/SP
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <button
                                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#fffbe6] text-[#faad14] text-sm font-medium leading-normal w-full mx-auto">
                                                <span class="truncate">Em Andamento</span>
                                            </button>
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            15/03/2024
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            30/03/2024
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[100px] text-sm font-normal leading-normal text-center">
                                            <a href="detalhesInspecao.html?id=INSP-001"
                                                class="text-blue-600 hover:underline">Ver Detalhes</a>
                                        </td>
                                    </tr>
                                    <tr class="border-t border-t-[#dce1e5]">
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            INSP-002
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            Rua Augusta, 2490 - Jardins, São Paulo/SP
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <button
                                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e6f7ff] text-[#007bff] text-sm font-medium leading-normal w-full mx-auto">
                                                <span class="truncate">Agendada</span>
                                            </button>
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            25/03/2024
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            10/04/2024
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[100px] text-sm font-normal leading-normal text-center">
                                            <a href="detalhesInspecao.html?id=INSP-002"
                                                class="text-blue-600 hover:underline">Ver Detalhes</a>
                                        </td>
                                    </tr>
                                    <tr class="border-t border-t-[#dce1e5]">
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            INSP-003
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            Rua das Flores, 800 - Vila Madalena, São Paulo/SP
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <button
                                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f6ffed] text-[#52c41a] text-sm font-medium leading-normal w-full mx-auto">
                                                <span class="truncate">Concluída</span>
                                            </button>
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            01/03/2024
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            15/03/2024
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[100px] text-sm font-normal leading-normal text-center">
                                            <a href="detalhesInspecao.html?id=INSP-003"
                                                class="text-blue-600 hover:underline">Ver Detalhes</a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const currentPath = window.location.pathname.split('/').pop();
            const navLinks = {
                'dashboard.html': 'Painel',
                'relatorios.html': 'Relatórios',
                'membros.html': 'Membros'
            };

            if (navLinks[currentPath]) {
                const activeLink = document.querySelector(`nav a[href="${currentPath}"]`);
                if (activeLink) {
                    activeLink.classList.add('nav-item-active');
                }
            }
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const statusFilter = document.getElementById('statusFilter');
            const startDateFilter = document.getElementById('startDateFilter');
            const endDateFilter = document.getElementById('endDateFilter');
            const searchInput = document.querySelector('input[placeholder="Buscar inspeções..."]'); // Corrigido o seletor
            const tableRows = document.querySelectorAll('tbody tr');

            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const statusValue = statusFilter.value;
                const startDateValue = startDateFilter.value;
                const endDateValue = endDateFilter.value;

                tableRows.forEach(row => {
                    const idText = row.cells[0].textContent.toLowerCase();
                    const addressText = row.cells[1].textContent.toLowerCase();
                    const statusText = row.cells[2].textContent.trim();
                    const dateText = row.cells[3].textContent.trim();

                    const [day, month, year] = dateText.split('/');
                    const inspectionDate = new Date(`${year}-${month}-${day}`);

                    let matchesSearch = true;
                    if (searchTerm) {
                        matchesSearch = idText.includes(searchTerm) || addressText.includes(searchTerm);
                    }

                    let matchesStatus = true;
                    if (statusValue) {
                        matchesStatus = statusText === statusValue;
                    }

                    let matchesStartDate = true;
                    if (startDateValue) {
                        const filterStartDate = new Date(startDateValue);
                        matchesStartDate = inspectionDate >= filterStartDate;
                    }

                    let matchesEndDate = true;
                    if (endDateValue) {
                        const filterEndDate = new Date(endDateValue);
                        matchesEndDate = inspectionDate <= filterEndDate;
                    }

                    if (matchesSearch && matchesStatus && matchesStartDate && matchesEndDate) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }

            statusFilter.addEventListener('change', filterTable);
            startDateFilter.addEventListener('input', filterTable);
            endDateFilter.addEventListener('input', filterTable);
            searchInput.addEventListener('input', filterTable);
        });

        function formatDate(dateString) {
            if (!dateString) return '';
            const parts = dateString.split('/');
            if (parts.length === 3) {
                return `${parts[0]}/${parts[1]}/${parts[2]}`;
            }
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }

        document.addEventListener('DOMContentLoaded', function () {
            const tableRows = document.querySelectorAll("tbody tr");
            tableRows.forEach(row => {
                const dateCell = row.cells[3];
                if (dateCell) {
                    dateCell.textContent = formatDate(dateCell.textContent);
                }
                const endDateCell = row.cells[4];
                if (endDateCell) {
                    endDateCell.textContent = formatDate(endDateCell.textContent);
                }
            });
        });
    </script>    <script>        const inspectionsData = {
            "INSP-001": {
                id: "INSP-001",
                name: "Inspeção Edifício Central",
                client: "Edifício Central",
                address: "Av. Paulista, 1500 - Bela Vista, São Paulo/SP",
                type: "Estrutural",
                status: "Em Andamento",
                creationDate: "01/03/2024",
                startDate: "15/03/2024",
                endDate: "30/03/2024",
                coordinator: "Dr. Carlos Silva",
                engineer: "Dr. Carlos Silva",
                description: "Inspeção estrutural completa do edifício comercial incluindo análise de fundações, pilares, vigas e lajes.",
                contactName: "Ana Paula Santos",
                contactEmail: "<EMAIL>",
                contactPhone: "(11) 98765-4321",
                progress: 45,
                inspectors: ["João Kleber", "Maria Fernanda"]
            },
            "INSP-002": {
                id: "INSP-002",
                name: "Inspeção Shopping Plaza",
                client: "Shopping Plaza",
                address: "Rua Augusta, 2490 - Jardins, São Paulo/SP",
                type: "Elétrica",
                status: "Agendada",
                creationDate: "05/03/2024",
                startDate: "25/03/2024",
                endDate: "10/04/2024",
                coordinator: "Eng. Maria Santos",
                engineer: "Eng. João Silva",
                description: "Inspeção preventiva das instalações elétricas do shopping center, incluindo quadros de distribuição e sistemas de segurança.",
                contactName: "Pedro Costa",
                contactEmail: "<EMAIL>",
                contactPhone: "(11) 91234-5678",
                progress: 0,
                inspectors: ["Carlos Mendes", "Ana Lima"]
            },
            "INSP-003": {
                id: "INSP-003",
                name: "Inspeção Residencial Aurora",
                client: "Condomínio Residencial Aurora",
                address: "Rua das Flores, 800 - Vila Madalena, São Paulo/SP",
                type: "Hidráulica",
                status: "Concluída",
                creationDate: "20/02/2024",
                startDate: "01/03/2024",
                endDate: "15/03/2024",
                coordinator: "Eng. Ana Lima",
                engineer: "Dr. Carlos Silva",
                description: "Inspeção completa do sistema hidráulico do condomínio residencial, incluindo reservatórios, tubulações e sistemas de bombeamento.",
                contactName: "Roberto Silva",
                contactEmail: "<EMAIL>",
                contactPhone: "(11) 95555-4444",
                progress: 100,
                inspectors: ["Fernanda Rocha", "Paulo Santos"]
            }
        };function populateInspectionsTable() {
            const tableBody = document.querySelector('tbody');
            tableBody.innerHTML = '';

            Object.values(inspectionsData).forEach(inspection => {
                const row = document.createElement('tr');
                row.classList.add('border-t', 'border-t-[#dce1e5]');

                row.innerHTML = `
                    <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">${inspection.id}</td>
                    <td class="h-[72px] px-4 py-2 w-[250px] text-[#637688] text-sm font-normal leading-normal text-center">${inspection.address}</td>
                    <td class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                        <button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 ${getStatusColor(inspection.status)} text-sm font-medium leading-normal w-full mx-auto">
                            <span class="truncate">${inspection.status}</span>
                        </button>
                    </td>
                    <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">${inspection.startDate}</td>
                    <td class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">${inspection.endDate || 'Não Definida'}</td>
                    <td class="h-[72px] px-4 py-2 w-[100px] text-sm font-normal leading-normal text-center">
                        <a href="detalhesInspecao.html?id=${inspection.id}" class="text-blue-600 hover:underline">Ver Detalhes</a>
                    </td>
                `;

                tableBody.appendChild(row);
            });
        }        function getStatusColor(status) {
            switch (status) {
                case 'Ativo':
                    return 'bg-[#f6ffed] text-[#52c41a]';
                case 'Em Andamento':
                    return 'bg-[#fffbe6] text-[#faad14]';
                case 'Agendada':
                    return 'bg-[#e6f7ff] text-[#007bff]';
                case 'Concluída':
                    return 'bg-[#f6ffed] text-[#52c41a]';
                case 'Em Manutenção':
                    return 'bg-[#fff2e8] text-[#fa8c16]';
                case 'Inativo':
                    return 'bg-[#f5f5f5] text-[#8c8c8c]';
                default:
                    return 'bg-[#f5f5f5] text-[#8c8c8c]';
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            populateInspectionsTable();
        });
    </script>
</body>

</html>