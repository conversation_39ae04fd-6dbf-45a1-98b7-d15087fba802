<html>

<head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Work+Sans%3Awght%40400%3B500%3B700%3B900" />

    <title>Cadastro</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Work Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111418]">
                    <h2 class="text-[#111418] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch</h2>
                </div>
            </header>
            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col items-center w-[512px] max-w-[512px] py-5 max-w-[960px] flex-1">
                    <h2
                        class="text-[#111418] tracking-light text-[28px] font-bold leading-tight px-4 text-center pb-3 pt-5">
                        Criar conta</h2>
                    <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                        <label class="flex flex-col min-w-40 flex-1">
                            <p class="text-[#111418] text-base font-medium leading-normal pb-2">Nome</p>
                            <input placeholder="Nome"
                                class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                                value="" />
                        </label>
                    </div>
                    <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                        <label class="flex flex-col min-w-40 flex-1">
                            <p class="text-[#111418] text-base font-medium leading-normal pb-2">Sobrenome</p>
                            <input placeholder="Sobrenome"
                                class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                                value="" />
                        </label>
                    </div>
                    <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                        <label class="flex flex-col min-w-40 flex-1">
                            <p class="text-[#111418] text-base font-medium leading-normal pb-2">Email</p>
                            <input placeholder="Email"
                                class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                                value="" />
                        </label>
                    </div>
                    <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                        <label class="flex flex-col min-w-40 flex-1">
                            <p class="text-[#111418] text-base font-medium leading-normal pb-2">Senha</p>
                            <input placeholder="Senha"
                                class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                                value="" />
                        </label>
                    </div>
                    <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                        <label class="flex flex-col min-w-40 flex-1">
                            <p class="text-[#111418] text-base font-medium leading-normal pb-2">Confirmar senha</p>
                            <input placeholder="Confirmar senha"
                                class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                                value="" />
                        </label>
                    </div>
                    <div class="flex w-full max-w-[480px] px-4 py-3">                        <a href="login.html"
                            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 flex-1 bg-[#1980e6] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                            <span class="truncate">Cadastrar</span>
                        </a>
                    </div>
                    <div class="mt-4 text-center px-4 pb-3 w-full max-w-[480px]">
                        <p class="text-sm text-[#637588] font-normal leading-normal">Já tem uma conta? <a href="login.html" class="text-[#1980e6] font-medium underline">Faça login</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>